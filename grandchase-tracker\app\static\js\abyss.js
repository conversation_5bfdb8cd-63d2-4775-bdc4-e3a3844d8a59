/**
 * GrandChase Tracker - Abyss JavaScript
 * Contains functionality for the Abyss Tracker tab
 */

// Update abyss completion status for a character
function updateAbyss(characterId, completed) {
    axios.post('/update_abyss', {
        character_id: characterId,
        completed: completed
    })
    .then(function (response) {
        if (response.data.success) {
            console.log('Abyss status updated successfully');
            updateAbyssCount();
            showToast(`Abyss ${completed ? 'completed' : 'reset'} for character #${characterId}`);
        } else {
            console.error('Failed to update Abyss status:', response.data.error);
            showToast('Error updating Abyss status', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error updating Abyss status:', error);
        showToast('Error updating Abyss status', 'error');
    });
}

// Update abyss completion count
function updateAbyssCount() {
    axios.get('/get_abyss_count')
    .then(function (response) {
        if (response.data.success) {
            const completedCount = response.data.completed_count;
            const totalCharacters = response.data.total_characters;

            // Update counter with progress bar
            updateCounterWithProgressBar('abyss-completion', completedCount, totalCharacters);
        } else {
            console.error('Failed to get abyss count:', response.data.error);
            showToast('Error getting abyss count', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error getting abyss count:', error);
        showToast('Error getting abyss count', 'error');
    });
}

// Reset abyss completion for all characters
function resetAbyss() {
    Modal.confirm('Are you sure you want to reset Abyss? This will clear all Abyss completions for this week.', function() {
        axios.post('/reset_abyss')
        .then(function (response) {
            if (response.data.success) {
                console.log('Abyss reset successfully');

                // Uncheck all abyss checkboxes
                document.querySelectorAll('#abyss-tracker input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = false;
                });

                updateAbyssCount();
                showToast('All Abyss completions have been reset');
            } else {
                console.error('Failed to reset Abyss:', response.data.error);
                showToast('Error resetting Abyss', 'error');
            }
        })
        .catch(function (error) {
            console.error('Error resetting Abyss:', error);
            showToast('Error resetting Abyss', 'error');
        });
    });
}
