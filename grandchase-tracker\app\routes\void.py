"""
GrandChase Tracker - Void Routes
"""
from datetime import datetime, timezone, timedelta
from flask import Blueprint, request, jsonify, current_app
from app.models.database import get_db_connection

void_bp = Blueprint('void', __name__)

@void_bp.route('/update', methods=['POST'])
def update_void():
    """Update void raid status for a character"""
    data = request.get_json()
    character_id = data.get('character_id')
    void1 = data.get('void1', False)
    void2 = data.get('void2', False)
    void3 = data.get('void3', False)
    void1_clear_level = int(data.get('void1_clear_level', 3))
    void2_clear_level = int(data.get('void2_clear_level', 3))
    void3_clear_level = int(data.get('void3_clear_level', 3))

    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Check if there's an existing entry for this character
            cursor.execute(
                'SELECT id, void1, void2, void3, void1_clear_level, void2_clear_level, void3_clear_level FROM void_raids WHERE character_id = ?',
                (character_id,)
            )
            result = cursor.fetchone()

            # Get previous void status to handle unmarking
            previous_void1 = False
            previous_void2 = False
            previous_void3 = False
            previous_void1_level = void1_clear_level
            previous_void2_level = void2_clear_level
            previous_void3_level = void3_clear_level

            if result:  # If there's an existing entry
                previous_void1 = bool(result['void1'])
                previous_void2 = bool(result['void2'])
                previous_void3 = bool(result['void3'])
                previous_void1_level = result['void1_clear_level']
                previous_void2_level = result['void2_clear_level']
                previous_void3_level = result['void3_clear_level']

                # Update existing entry
                cursor.execute('''
                    UPDATE void_raids
                    SET void1 = ?, void2 = ?, void3 = ?,
                        void1_clear_level = ?, void2_clear_level = ?, void3_clear_level = ?
                    WHERE character_id = ?
                ''', (void1, void2, void3, void1_clear_level, void2_clear_level, void3_clear_level, character_id))
            else:
                # Create new entry
                cursor.execute('''
                    INSERT INTO void_raids
                    (character_id, void1, void2, void3, void1_clear_level, void2_clear_level, void3_clear_level, date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (character_id, void1, void2, void3, void1_clear_level, void2_clear_level, void3_clear_level, datetime.now().strftime('%Y-%m-%d')))

            conn.commit()

            # Calculate shards to add or remove based on floor level
            # 1F: 3 shards, 2F: 4.5 shards, 3F: 6 shards, 4F: 7.5 shards
            shard_values = {
                1: 3,    # 1F
                2: 4.5,  # 2F
                3: 6,    # 3F
                4: 7.5   # 4F
            }

            # Calculate shard changes
            shard_changes = {
                'void1': 0,
                'void2': 0,
                'void3': 0
            }

            # Handle void1 changes
            if void1 and not previous_void1:  # Marking as completed
                shard_changes['void1'] = shard_values.get(void1_clear_level, 0)
            elif not void1 and previous_void1:  # Unmarking
                shard_changes['void1'] = -shard_values.get(previous_void1_level, 0)

            # Handle void2 changes
            if void2 and not previous_void2:  # Marking as completed
                shard_changes['void2'] = shard_values.get(void2_clear_level, 0)
            elif not void2 and previous_void2:  # Unmarking
                shard_changes['void2'] = -shard_values.get(previous_void2_level, 0)

            # Handle void3 changes
            if void3 and not previous_void3:  # Marking as completed
                shard_changes['void3'] = shard_values.get(void3_clear_level, 0)
            elif not void3 and previous_void3:  # Unmarking
                shard_changes['void3'] = -shard_values.get(previous_void3_level, 0)

            # Get current shard counts
            cursor.execute(
                'SELECT void1_shards, void2_shards, void3_shards FROM shard_inventory WHERE character_id = ?',
                (character_id,)
            )
            shard_result = cursor.fetchone()

            if shard_result and (shard_changes['void1'] != 0 or shard_changes['void2'] != 0 or shard_changes['void3'] != 0):
                # Update shard counts
                new_void1_shards = max(0, shard_result['void1_shards'] + shard_changes['void1'])
                new_void2_shards = max(0, shard_result['void2_shards'] + shard_changes['void2'])
                new_void3_shards = max(0, shard_result['void3_shards'] + shard_changes['void3'])

                cursor.execute('''
                    UPDATE shard_inventory
                    SET void1_shards = ?, void2_shards = ?, void3_shards = ?
                    WHERE character_id = ?
                ''', (new_void1_shards, new_void2_shards, new_void3_shards, character_id))

                conn.commit()

                # Return the updated shard values
                return jsonify(
                    success=True,
                    void1_shards=new_void1_shards,
                    void2_shards=new_void2_shards,
                    void3_shards=new_void3_shards
                )

            return jsonify(success=True)
    except Exception as e:
        return jsonify(success=False, error=str(e))

@void_bp.route('/reset_void1', methods=['POST'])
def reset_void1():
    """Reset void1 status for all characters"""
    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Update all void1 entries
            cursor.execute('UPDATE void_raids SET void1 = 0')

            conn.commit()

        return jsonify(success=True)
    except Exception as e:
        return jsonify(success=False, error=str(e))

@void_bp.route('/reset_void2', methods=['POST'])
def reset_void2():
    """Reset void2 status for all characters"""
    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Update all void2 entries
            cursor.execute('UPDATE void_raids SET void2 = 0')

            conn.commit()

        return jsonify(success=True)
    except Exception as e:
        return jsonify(success=False, error=str(e))

@void_bp.route('/reset_void3', methods=['POST'])
def reset_void3():
    """Reset void3 status for all characters"""
    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Update all void3 entries
            cursor.execute('UPDATE void_raids SET void3 = 0')

            conn.commit()

        return jsonify(success=True)
    except Exception as e:
        return jsonify(success=False, error=str(e))

@void_bp.route('/get_void_data', methods=['GET'])
def get_void_data():
    """Get void raid data for a specific character"""
    try:
        character_id = request.args.get('character_id')

        if not character_id:
            return jsonify(success=False, error="Missing character_id parameter")

        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Get void raid data for the character
            cursor.execute('''
                SELECT void1, void2, void3, void1_clear_level, void2_clear_level, void3_clear_level
                FROM void_raids
                WHERE character_id = ?
            ''', (character_id,))

            result = cursor.fetchone()

            if result:
                return jsonify(
                    success=True,
                    void1=bool(result['void1']),
                    void2=bool(result['void2']),
                    void3=bool(result['void3']),
                    void1_clear_level=result['void1_clear_level'],
                    void2_clear_level=result['void2_clear_level'],
                    void3_clear_level=result['void3_clear_level']
                )
            else:
                # If no record exists, return default values
                return jsonify(
                    success=True,
                    void1=False,
                    void2=False,
                    void3=False,
                    void1_clear_level=3,
                    void2_clear_level=3,
                    void3_clear_level=3
                )
    except Exception as e:
        return jsonify(success=False, error=str(e))

@void_bp.route('/get_void_run_counts', methods=['GET'])
def get_void_run_counts():
    """Get the count of void runs"""
    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Get counts for all characters
            cursor.execute('''
                SELECT
                    SUM(CASE WHEN void1 = 1 THEN 1 ELSE 0 END) as void1_count,
                    SUM(CASE WHEN void2 = 1 THEN 1 ELSE 0 END) as void2_count,
                    SUM(CASE WHEN void3 = 1 THEN 1 ELSE 0 END) as void3_count,
                    COUNT(*) as total_characters
                FROM void_raids
            ''')

            result = cursor.fetchone()

            # Get total character count
            cursor.execute('SELECT COUNT(*) as total FROM characters')
            total_result = cursor.fetchone()

            void1_count = result['void1_count'] or 0
            void2_count = result['void2_count'] or 0
            void3_count = result['void3_count'] or 0
            total_characters = total_result['total']

            return jsonify(
                success=True,
                void1_count=void1_count,
                void2_count=void2_count,
                void3_count=void3_count,
                total_characters=total_characters
            )
    except Exception as e:
        return jsonify(success=False, error=str(e))

@void_bp.route('/update_shards', methods=['POST'])
def update_shards():
    """Update shard inventory for a character"""
    data = request.get_json()
    character_id = data.get('character_id')
    shard_type = data.get('shard_type')
    shard_value = data.get('shard_value')

    if shard_type not in ['void1', 'void2', 'void3']:
        return jsonify(success=False, error='Invalid shard type')

    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Map shard type to column name
            shard_column = {
                'void1': 'void1_shards',
                'void2': 'void2_shards',
                'void3': 'void3_shards'
            }[shard_type]

            # Update shard inventory
            cursor.execute(f'''
                UPDATE shard_inventory
                SET {shard_column} = ?
                WHERE character_id = ?
            ''', (shard_value, character_id))

            conn.commit()

            # Return the updated shard value
            return jsonify(success=True, shard_value=shard_value)
    except Exception as e:
        return jsonify(success=False, error=str(e))

@void_bp.route('/convert_to_v3', methods=['POST'])
def convert_to_v3():
    """Convert void1 and void2 shards to void3 shards"""
    data = request.get_json()
    character_id = data.get('character_id')
    v1_convert = data.get('v1_convert', 0)
    v2_convert = data.get('v2_convert', 0)

    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Get current shard counts
            cursor.execute(
                'SELECT void1_shards, void2_shards, void3_shards FROM shard_inventory WHERE character_id = ?',
                (character_id,)
            )
            result = cursor.fetchone()

            if result:
                void1_shards = result['void1_shards']
                void2_shards = result['void2_shards']
                void3_shards = result['void3_shards']

                # Check if enough shards are available
                if void1_shards < v1_convert or void2_shards < v2_convert:
                    return jsonify(
                        success=False,
                        error='Not enough shards available for conversion'
                    )

                # Calculate new shard counts
                new_void1_shards = void1_shards - v1_convert
                new_void2_shards = void2_shards - v2_convert

                # Conversion ratio: 4 V1 shards = 1 V3 shard, 4 V2 shards = 1 V3 shard
                v1_to_v3 = v1_convert // 4  # Integer division to get whole number of V3 shards
                v2_to_v3 = v2_convert // 4  # Integer division to get whole number of V3 shards

                new_void3_shards = void3_shards + v1_to_v3 + v2_to_v3

                # Update shard inventory
                cursor.execute('''
                    UPDATE shard_inventory
                    SET void1_shards = ?, void2_shards = ?, void3_shards = ?
                    WHERE character_id = ?
                ''', (new_void1_shards, new_void2_shards, new_void3_shards, character_id))

                conn.commit()

                return jsonify(
                    success=True,
                    void1_shards=new_void1_shards,
                    void2_shards=new_void2_shards,
                    void3_shards=new_void3_shards
                )
            else:
                return jsonify(success=False, error='Character not found in shard inventory')
    except Exception as e:
        return jsonify(success=False, error=str(e))

@void_bp.route('/craft_void_gear', methods=['POST'])
def craft_void_gear():
    """Craft gear using shards (Void Tracker version - doesn't update gear database)"""
    data = request.get_json()
    character_id = data.get('character_id')
    gear_type = data.get('gear_type')  # 'void1', 'void2', or 'void3'
    reduction = data.get('reduction', 60)  # Default to 60 fragments

    # Map gear type to corresponding shard column
    shard_columns = {
        'void1': 'void1_shards',
        'void2': 'void2_shards',
        'void3': 'void3_shards'
    }

    if gear_type not in shard_columns:
        return jsonify(success=False, error=f"Invalid gear type: {gear_type}")

    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Get current shard count
            shard_column = shard_columns[gear_type]
            cursor.execute(f'''
                SELECT {shard_column} FROM shard_inventory WHERE character_id = ?
            ''', (character_id,))

            result = cursor.fetchone()

            if not result:
                return jsonify(success=False, error="Character not found in shard inventory")

            current_shards = result[shard_column]

            # Check if enough shards are available
            if current_shards < reduction:
                return jsonify(
                    success=False,
                    error=f"Not enough {gear_type} shards. Required: {reduction}, Available: {current_shards}"
                )

            # Update shard inventory
            new_shard_count = current_shards - reduction
            cursor.execute(f'''
                UPDATE shard_inventory
                SET {shard_column} = ?
                WHERE character_id = ?
            ''', (new_shard_count, character_id))

            conn.commit()

            # Get updated shard counts
            cursor.execute('''
                SELECT void1_shards, void2_shards, void3_shards
                FROM shard_inventory
                WHERE character_id = ?
            ''', (character_id,))

            updated_shards = cursor.fetchone()

            return jsonify(
                success=True,
                void1_shards=updated_shards['void1_shards'],
                void2_shards=updated_shards['void2_shards'],
                void3_shards=updated_shards['void3_shards']
            )
    except Exception as e:
        return jsonify(success=False, error=str(e))