# GrandChase Tracker

A clean, modern web application for tracking GrandChase game activities.

## Features

- **Void Tracker**: Track Void 1, 2, and 3 raid completions and shard inventory
- **TA Tracker**: Track TA scores and view tier list
- **Gear Tracker**: Track gear acquisition and craft gear with shards
- **Abyss Tracker**: Track Abyss completions
- **ToD Tracker**: Track Tower of Disappearance runs, including floor completion and drops
- **Accessory Tracker**: Track rings, earrings, and piercings
- **Dark Mode**: Toggle between light and dark themes
- **Responsive Design**: Works on desktop and mobile devices

## Installation

### Prerequisites

- Python 3.7 or higher
- pip (Python package installer)

### Setup

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/grandchase-tracker.git
   cd grandchase-tracker
   ```

2. Create a virtual environment (optional but recommended):
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Run the application:
   ```
   python main.py
   ```

5. Open your browser and go to:
   ```
   http://localhost:5000
   ```

## Usage

- Use the tabs at the top to navigate between different trackers
- Click the "Toggle Dark Mode" button to switch between light and dark themes
- Use the reset buttons to clear completions for each tracker

### Void Tracker

- Check the checkbox to mark a raid as completed
- Select the floor level (1F, 2F, 3F) for each raid
- Update shard counts manually or by completing raids
- Convert Void 1 and Void 2 shards to Void 3 shards

### TA Tracker

- Enter TA scores for each character
- Click "Show/Hide TA Tier List" to view characters ranked by TA score

### Gear Tracker

- Check the checkboxes to mark gear as acquired
- Use the craft buttons to craft gear using shards

### Abyss Tracker

- Check the checkbox to mark Abyss as completed for a character

### ToD Tracker

- Select the floor (5F or 10F) for each character
- Track SPR drops for each run
- Check the checkbox to mark ToD as completed for a character

### Accessory Tracker

- Select accessories for each character from the dropdown menus

## Building an Executable

To build a standalone executable:

1. Install PyInstaller:
   ```
   pip install pyinstaller
   ```

2. Build the executable:
   ```
   pyinstaller --onefile --add-data "app/static;static" --add-data "app/templates;templates" main.py
   ```

3. The executable will be created in the `dist` directory

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- GrandChase game and all related assets are property of their respective owners
- This application is for personal use only and is not affiliated with the official game
