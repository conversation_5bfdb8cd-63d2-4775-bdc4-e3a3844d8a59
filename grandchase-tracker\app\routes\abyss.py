"""
GrandChase Tracker - Abyss Routes
"""
from flask import Blueprint, request, jsonify, current_app
from app.models.database import get_db_connection

abyss_bp = Blueprint('abyss', __name__)

@abyss_bp.route('/update_abyss', methods=['POST'])
def update_abyss():
    """Update abyss completion status for a character"""
    data = request.get_json()
    character_id = data.get('character_id')
    completed = data.get('completed')

    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Update abyss status
            cursor.execute('''
                UPDATE abyss_runs
                SET completed = ?
                WHERE character_id = ?
            ''', (completed, character_id))

            # If no rows were updated, insert a new record
            if cursor.rowcount == 0:
                cursor.execute('''
                    INSERT INTO abyss_runs (character_id, completed)
                    VALUES (?, ?)
                ''', (character_id, completed))

            conn.commit()

            return jsonify(success=True)
    except Exception as e:
        return jsonify(success=False, error=str(e))

@abyss_bp.route('/reset_abyss', methods=['POST'])
def reset_abyss():
    """Reset abyss completion status for all characters"""
    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Reset all abyss completions
            cursor.execute('UPDATE abyss_runs SET completed = 0')

            conn.commit()

            return jsonify(success=True)
    except Exception as e:
        return jsonify(success=False, error=str(e))

@abyss_bp.route('/get_abyss_count', methods=['GET'])
def get_abyss_count():
    """Get count of completed abyss runs"""
    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Get total character count
            cursor.execute('SELECT COUNT(*) as total FROM characters')
            total_characters = cursor.fetchone()['total']

            # Get completed abyss count
            cursor.execute('SELECT COUNT(*) as completed FROM abyss_runs WHERE completed = 1')
            completed_count = cursor.fetchone()['completed'] or 0

            return jsonify(
                success=True,
                completed_count=completed_count,
                total_characters=total_characters
            )
    except Exception as e:
        return jsonify(success=False, error=str(e))

@abyss_bp.route('/get_abyss_data', methods=['GET'])
def get_abyss_data():
    """Get abyss completion data for a specific character"""
    try:
        character_id = request.args.get('character_id')

        if not character_id:
            return jsonify(success=False, error="Missing character_id parameter")

        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Get abyss completion status
            cursor.execute('''
                SELECT completed
                FROM abyss_runs
                WHERE character_id = ?
            ''', (character_id,))

            result = cursor.fetchone()

            if result:
                return jsonify(
                    success=True,
                    completed=bool(result['completed'])
                )
            else:
                # If no record exists, return default value
                return jsonify(
                    success=True,
                    completed=False
                )
    except Exception as e:
        return jsonify(success=False, error=str(e))
