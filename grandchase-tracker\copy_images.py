#!/usr/bin/env python3
"""
Script to copy images from the original project to the new project.
"""
import os
import shutil
import sys

def copy_images(source_dir, dest_dir):
    """Copy images from source directory to destination directory"""
    # Create destination directories if they don't exist
    os.makedirs(os.path.join(dest_dir, 'app/static/images/characters'), exist_ok=True)
    os.makedirs(os.path.join(dest_dir, 'app/static/images/gear'), exist_ok=True)
    os.makedirs(os.path.join(dest_dir, 'app/static/images/accessories/rings'), exist_ok=True)
    os.makedirs(os.path.join(dest_dir, 'app/static/images/accessories/earrings'), exist_ok=True)
    os.makedirs(os.path.join(dest_dir, 'app/static/images/accessories/piercings'), exist_ok=True)
    
    # Copy character images
    source_char_dir = os.path.join(source_dir, 'static/images')
    dest_char_dir = os.path.join(dest_dir, 'app/static/images/characters')
    
    if os.path.exists(source_char_dir):
        for filename in os.listdir(source_char_dir):
            if filename.endswith('.png') and not filename.endswith('_icon.png'):
                source_file = os.path.join(source_char_dir, filename)
                dest_file = os.path.join(dest_char_dir, filename)
                shutil.copy2(source_file, dest_file)
                print(f"Copied {filename} to {dest_char_dir}")
    
    # Copy gear icons
    source_gear_icons = [
        os.path.join(source_dir, 'static/images/weapon_icon.png'),
        os.path.join(source_dir, 'static/images/helmet_icon.png'),
        os.path.join(source_dir, 'static/images/armor_icon.png'),
        os.path.join(source_dir, 'static/images/pants_icon.png'),
        os.path.join(source_dir, 'static/images/gloves_icon.png'),
        os.path.join(source_dir, 'static/images/shoes_icon.png'),
        os.path.join(source_dir, 'static/images/cloak_icon.png'),
        os.path.join(source_dir, 'static/images/cape_icon.png')
    ]
    
    dest_gear_dir = os.path.join(dest_dir, 'app/static/images/gear')
    
    for source_file in source_gear_icons:
        if os.path.exists(source_file):
            filename = os.path.basename(source_file)
            dest_file = os.path.join(dest_gear_dir, filename)
            shutil.copy2(source_file, dest_file)
            print(f"Copied {filename} to {dest_gear_dir}")
    
    # Copy accessory icons if they exist
    source_acc_dir = os.path.join(source_dir, 'static/images/accessories')
    
    if os.path.exists(source_acc_dir):
        # Copy rings
        source_rings_dir = os.path.join(source_acc_dir, 'rings')
        dest_rings_dir = os.path.join(dest_dir, 'app/static/images/accessories/rings')
        
        if os.path.exists(source_rings_dir):
            for filename in os.listdir(source_rings_dir):
                if filename.endswith('.png'):
                    source_file = os.path.join(source_rings_dir, filename)
                    dest_file = os.path.join(dest_rings_dir, filename)
                    shutil.copy2(source_file, dest_file)
                    print(f"Copied {filename} to {dest_rings_dir}")
        
        # Copy earrings
        source_earrings_dir = os.path.join(source_acc_dir, 'earrings')
        dest_earrings_dir = os.path.join(dest_dir, 'app/static/images/accessories/earrings')
        
        if os.path.exists(source_earrings_dir):
            for filename in os.listdir(source_earrings_dir):
                if filename.endswith('.png'):
                    source_file = os.path.join(source_earrings_dir, filename)
                    dest_file = os.path.join(dest_earrings_dir, filename)
                    shutil.copy2(source_file, dest_file)
                    print(f"Copied {filename} to {dest_earrings_dir}")
        
        # Copy piercings
        source_piercings_dir = os.path.join(source_acc_dir, 'piercings')
        dest_piercings_dir = os.path.join(dest_dir, 'app/static/images/accessories/piercings')
        
        if os.path.exists(source_piercings_dir):
            for filename in os.listdir(source_piercings_dir):
                if filename.endswith('.png'):
                    source_file = os.path.join(source_piercings_dir, filename)
                    dest_file = os.path.join(dest_piercings_dir, filename)
                    shutil.copy2(source_file, dest_file)
                    print(f"Copied {filename} to {dest_piercings_dir}")
    
    # Copy favicon
    source_favicon = os.path.join(source_dir, 'static/favicon.ico')
    dest_favicon = os.path.join(dest_dir, 'app/static/favicon.ico')
    
    if os.path.exists(source_favicon):
        shutil.copy2(source_favicon, dest_favicon)
        print(f"Copied favicon.ico to {os.path.dirname(dest_favicon)}")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python copy_images.py <source_directory>")
        sys.exit(1)
    
    source_dir = sys.argv[1]
    dest_dir = os.path.dirname(os.path.abspath(__file__))
    
    copy_images(source_dir, dest_dir)
