/**
 * GrandChase Tracker - Overview JavaScript
 * Contains functionality for the Overview tab
 */

// Update TA from overview card
function updateTAFromOverview(characterId) {
    const taInput = document.getElementById(`overview-ta-${characterId}`);
    const ta = parseInt(taInput.value);

    axios.post('/update_ta', {
        character_id: characterId,
        ta: ta
    })
    .then(function (response) {
        if (response.data.success) {
            console.log('TA updated successfully from overview');

            // Update the TA input in the TA tracker tab if it exists
            const taTrackerInput = document.getElementById(`ta-input-${characterId}`);
            if (taTrackerInput) {
                taTrackerInput.value = ta;
            }

            // Update overview summary stats
            updateOverviewStats();

            showToast(`Updated TA for character #${characterId}`);
        } else {
            console.error('Failed to update TA:', response.data.error);
            showToast('Error updating TA', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error updating TA:', error);
        showToast('Error updating TA', 'error');
    });
}

// Toggle void completion from overview
function toggleVoidFromOverview(characterId, voidNumber) {
    // Get current status
    const statusElement = document.getElementById(`overview-void${voidNumber}-${characterId}`);
    const isCurrentlyCompleted = statusElement.classList.contains('completed');

    // Toggle the status
    const newStatus = !isCurrentlyCompleted;

    // Update via the existing void update mechanism
    updateVoidFromOverview(characterId, voidNumber, newStatus);
}

// Update void status from overview
function updateVoidFromOverview(characterId, voidNumber, completed) {
    // Get the current void checkboxes from the void tracker tab
    const voidCheckbox = document.getElementById(`void${voidNumber}-${characterId}`);

    if (voidCheckbox) {
        voidCheckbox.checked = completed;

        // Trigger the existing void update function
        updateVoid(characterId);

        // Update the overview display
        updateOverviewVoidStatus(characterId, voidNumber, completed);

        // Update overview summary stats
        updateOverviewStats();
    } else {
        // If void tracker elements don't exist, make direct API call
        const voidData = {
            character_id: characterId,
            [`void${voidNumber}`]: completed,
            [`void${voidNumber}_clear_level`]: 3 // Default to 3F
        };

        // Add other void statuses to maintain current state
        for (let i = 1; i <= 3; i++) {
            if (i !== voidNumber) {
                const otherCheckbox = document.getElementById(`void${i}-${characterId}`);
                if (otherCheckbox) {
                    voidData[`void${i}`] = otherCheckbox.checked;
                    const levelSelect = document.getElementById(`void${i}-level-${characterId}`);
                    if (levelSelect) {
                        voidData[`void${i}_clear_level`] = parseInt(levelSelect.value.charAt(0));
                    } else {
                        voidData[`void${i}_clear_level`] = 3;
                    }
                } else {
                    // Get current status from overview
                    const overviewStatus = document.getElementById(`overview-void${i}-${characterId}`);
                    voidData[`void${i}`] = overviewStatus ? overviewStatus.classList.contains('completed') : false;
                    voidData[`void${i}_clear_level`] = 3;
                }
            }
        }

        axios.post('/update', voidData)
        .then(function (response) {
            if (response.data.success) {
                console.log('Void status updated successfully from overview');
                updateOverviewVoidStatus(characterId, voidNumber, completed);
                updateOverviewStats();
                updateVoidRunCounts();
                showToast(`Void ${voidNumber} ${completed ? 'completed' : 'reset'} for character #${characterId}`);
            } else {
                console.error('Failed to update void status:', response.data.error);
                showToast('Error updating void status', 'error');
            }
        })
        .catch(function (error) {
            console.error('Error updating void status:', error);
            showToast('Error updating void status', 'error');
        });
    }
}

// Toggle abyss completion from overview
function toggleAbyssFromOverview(characterId) {
    // Get current status
    const statusElement = document.getElementById(`overview-abyss-${characterId}`);
    const isCurrentlyCompleted = statusElement.classList.contains('completed');

    // Toggle the status
    const newStatus = !isCurrentlyCompleted;

    updateAbyssFromOverview(characterId, newStatus);
}

// Update abyss status from overview
function updateAbyssFromOverview(characterId, completed) {
    // Get the abyss checkbox from the abyss tracker tab
    const abyssCheckbox = document.querySelector(`#abyss-tracker tr[data-character-id="${characterId}"] input[type="checkbox"]`);

    if (abyssCheckbox) {
        abyssCheckbox.checked = completed;

        // Trigger the existing abyss update function
        updateAbyss(characterId, completed);

        // Update the overview display
        updateOverviewAbyssStatus(characterId, completed);

        // Update overview summary stats
        updateOverviewStats();
    } else {
        // If abyss tracker elements don't exist, make direct API call
        axios.post('/update_abyss', {
            character_id: characterId,
            completed: completed
        })
        .then(function (response) {
            if (response.data.success) {
                console.log('Abyss status updated successfully from overview');
                updateOverviewAbyssStatus(characterId, completed);
                updateOverviewStats();
                updateAbyssCount();
                showToast(`Abyss ${completed ? 'completed' : 'reset'} for character #${characterId}`);
            } else {
                console.error('Failed to update abyss status:', response.data.error);
                showToast('Error updating abyss status', 'error');
            }
        })
        .catch(function (error) {
            console.error('Error updating abyss status:', error);
            showToast('Error updating abyss status', 'error');
        });
    }
}

// Update overview void status display
function updateOverviewVoidStatus(characterId, voidNumber, completed) {
    const statusElement = document.getElementById(`overview-void${voidNumber}-${characterId}`);
    if (statusElement) {
        statusElement.textContent = completed ? 'Completed' : 'Incomplete';
        statusElement.className = `void-status ${completed ? 'completed' : 'incomplete'}`;
    }

    // Update the button text and style
    updateVoidButton(characterId, voidNumber, completed);
}

// Update void button appearance based on completion status
function updateVoidButton(characterId, voidNumber, completed) {
    const button = document.getElementById(`void${voidNumber}-btn-${characterId}`);
    if (button) {
        const icon = button.querySelector('i');
        if (completed) {
            button.className = 'action-btn void-toggle-btn reset';
            button.innerHTML = '<i class="fas fa-times"></i> Reset V' + voidNumber;
        } else {
            button.className = 'action-btn void-toggle-btn complete';
            button.innerHTML = '<i class="fas fa-check"></i> Complete V' + voidNumber;
        }
    }
}

// Update overview abyss status display
function updateOverviewAbyssStatus(characterId, completed) {
    const statusElement = document.getElementById(`overview-abyss-${characterId}`);
    if (statusElement) {
        statusElement.textContent = completed ? 'Completed' : 'Incomplete';
        statusElement.className = `abyss-status ${completed ? 'completed' : 'incomplete'}`;
    }

    // Update the button text and style
    updateAbyssButton(characterId, completed);
}

// Update abyss button appearance based on completion status
function updateAbyssButton(characterId, completed) {
    const button = document.getElementById(`abyss-btn-${characterId}`);
    if (button) {
        if (completed) {
            button.className = 'action-btn abyss-toggle-btn reset';
            button.innerHTML = '<i class="fas fa-times"></i> Reset Abyss';
        } else {
            button.className = 'action-btn abyss-toggle-btn complete';
            button.innerHTML = '<i class="fas fa-check"></i> Complete Abyss';
        }
    }
}

// Open accessory modal (placeholder for future implementation)
function openAccessoryModal(characterId) {
    showToast('Accessory editing modal - Coming soon!', 'info');
    // TODO: Implement accessory editing modal
}

// Update overview summary statistics
function updateOverviewStats() {
    // Count void completions (only in overview tab to avoid counting other tabs)
    let voidCompletions = 0;
    document.querySelectorAll('#overview .void-status.completed').forEach(() => voidCompletions++);

    // Count abyss completions (only in overview tab)
    let abyssCompletions = 0;
    document.querySelectorAll('#overview .abyss-status.completed').forEach(() => abyssCompletions++);

    // Calculate average TA
    let totalTA = 0;
    let characterCount = 0;
    document.querySelectorAll('.ta-card-input').forEach(input => {
        totalTA += parseInt(input.value) || 0;
        characterCount++;
    });
    const avgTA = characterCount > 0 ? Math.round(totalTA / characterCount) : 0;

    // Update displays
    const voidCompletionsElement = document.getElementById('overview-void-completions');
    const abyssCompletionsElement = document.getElementById('overview-abyss-completions');
    const avgTAElement = document.getElementById('overview-avg-ta');

    if (voidCompletionsElement) voidCompletionsElement.textContent = voidCompletions;
    if (abyssCompletionsElement) abyssCompletionsElement.textContent = abyssCompletions;
    if (avgTAElement) avgTAElement.textContent = avgTA.toLocaleString();

    console.log(`Overview stats updated: Void: ${voidCompletions}, Abyss: ${abyssCompletions}, Avg TA: ${avgTA}`);
}

// Load overview data for all characters
function loadOverviewData() {
    console.log('Loading overview data for all characters');

    // Add a small delay to ensure DOM is fully rendered
    setTimeout(() => {
        updateOverviewStats();
    }, 100);

    // Only load dynamic data if needed (for real-time updates)
    // The initial data is already loaded from the template
}

// Load overview data for a specific character
function loadOverviewCharacterData(characterId) {
    // Load void data
    axios.get(`/get_void_data?character_id=${characterId}`)
    .then(function (response) {
        if (response.data.success) {
            updateOverviewVoidStatus(characterId, 1, response.data.void1);
            updateOverviewVoidStatus(characterId, 2, response.data.void2);
            updateOverviewVoidStatus(characterId, 3, response.data.void3);

            // Update shards display
            updateOverviewShards(characterId, {
                void1_shards: response.data.void1_shards || 0,
                void2_shards: response.data.void2_shards || 0,
                void3_shards: response.data.void3_shards || 0
            });
        }
    })
    .catch(function (error) {
        console.error(`Error loading void data for character ${characterId}:`, error);
    });

    // Load abyss data
    axios.get(`/get_abyss_data?character_id=${characterId}`)
    .then(function (response) {
        if (response.data.success) {
            updateOverviewAbyssStatus(characterId, response.data.completed);
        }
    })
    .catch(function (error) {
        console.error(`Error loading abyss data for character ${characterId}:`, error);
    });
}

// Update overview shards display
function updateOverviewShards(characterId, shards) {
    if (shards.void1_shards !== undefined) {
        const shards1Element = document.getElementById(`overview-shards1-${characterId}`);
        if (shards1Element) shards1Element.textContent = shards.void1_shards;
    }

    if (shards.void2_shards !== undefined) {
        const shards2Element = document.getElementById(`overview-shards2-${characterId}`);
        if (shards2Element) shards2Element.textContent = shards.void2_shards;
    }

    if (shards.void3_shards !== undefined) {
        const shards3Element = document.getElementById(`overview-shards3-${characterId}`);
        if (shards3Element) shards3Element.textContent = shards.void3_shards;
    }
}

// Update overview TA display
function updateOverviewTA(characterId, ta) {
    const taInput = document.getElementById(`overview-ta-${characterId}`);
    if (taInput) {
        taInput.value = ta;
    }
}

// Refresh overview data for a character (called from other tabs)
function refreshOverviewCharacter(characterId) {
    if (document.getElementById('overview').style.display === 'block') {
        loadOverviewCharacterData(characterId);
    }
}

// Initialize overview when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Overview tab');

    // Load overview data when the page loads
    setTimeout(() => loadOverviewData(), 100);
});
