/**
 * GrandChase Tracker - Overview JavaScript
 * Contains functionality for the Overview tab
 */

// Update TA from overview card
function updateTAFromOverview(characterId) {
    const taInput = document.getElementById(`overview-ta-${characterId}`);
    const ta = parseInt(taInput.value);

    axios.post('/update_ta', {
        character_id: characterId,
        ta: ta
    })
    .then(function (response) {
        if (response.data.success) {
            console.log('TA updated successfully from overview');

            // Update the TA input in the TA tracker tab if it exists
            const taTrackerInput = document.getElementById(`ta-input-${characterId}`);
            if (taTrackerInput) {
                taTrackerInput.value = ta;
            }

            // Update overview summary stats
            updateOverviewStats();

            showToast(`Updated TA for character #${characterId}`);
        } else {
            console.error('Failed to update TA:', response.data.error);
            showToast('Error updating TA', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error updating TA:', error);
        showToast('Error updating TA', 'error');
    });
}

// Toggle void completion from overview
function toggleVoidFromOverview(characterId, voidNumber) {
    // Get current status
    const statusElement = document.getElementById(`overview-void${voidNumber}-${characterId}`);
    const isCurrentlyCompleted = statusElement.classList.contains('completed');

    // Toggle the status
    const newStatus = !isCurrentlyCompleted;

    // Update via the existing void update mechanism
    updateVoidFromOverview(characterId, voidNumber, newStatus);
}

// Update void status from overview
function updateVoidFromOverview(characterId, voidNumber, completed) {
    // Get the current void checkboxes from the void tracker tab
    const voidCheckbox = document.getElementById(`void${voidNumber}-${characterId}`);

    if (voidCheckbox) {
        voidCheckbox.checked = completed;

        // Trigger the existing void update function
        updateVoid(characterId);

        // Update the overview display
        updateOverviewVoidStatus(characterId, voidNumber, completed);

        // Update overview summary stats
        updateOverviewStats();
    } else {
        // If void tracker elements don't exist, make direct API call
        const voidData = {
            character_id: characterId,
            [`void${voidNumber}`]: completed,
            [`void${voidNumber}_clear_level`]: 3 // Default to 3F
        };

        // Add other void statuses to maintain current state
        for (let i = 1; i <= 3; i++) {
            if (i !== voidNumber) {
                const otherCheckbox = document.getElementById(`void${i}-${characterId}`);
                if (otherCheckbox) {
                    voidData[`void${i}`] = otherCheckbox.checked;
                    const levelSelect = document.getElementById(`void${i}-level-${characterId}`);
                    if (levelSelect) {
                        voidData[`void${i}_clear_level`] = parseInt(levelSelect.value.charAt(0));
                    } else {
                        voidData[`void${i}_clear_level`] = 3;
                    }
                } else {
                    // Get current status from overview
                    const overviewStatus = document.getElementById(`overview-void${i}-${characterId}`);
                    voidData[`void${i}`] = overviewStatus ? overviewStatus.classList.contains('completed') : false;
                    voidData[`void${i}_clear_level`] = 3;
                }
            }
        }

        axios.post('/update', voidData)
        .then(function (response) {
            if (response.data.success) {
                console.log('Void status updated successfully from overview');
                updateOverviewVoidStatus(characterId, voidNumber, completed);
                updateOverviewStats();
                updateVoidRunCounts();
                updateFiltersOnStatusChange();
                showToast(`Void ${voidNumber} ${completed ? 'completed' : 'reset'} for character #${characterId}`);
            } else {
                console.error('Failed to update void status:', response.data.error);
                showToast('Error updating void status', 'error');
            }
        })
        .catch(function (error) {
            console.error('Error updating void status:', error);
            showToast('Error updating void status', 'error');
        });
    }
}

// Toggle abyss completion from overview
function toggleAbyssFromOverview(characterId) {
    // Get current status
    const statusElement = document.getElementById(`overview-abyss-${characterId}`);
    const isCurrentlyCompleted = statusElement.classList.contains('completed');

    // Toggle the status
    const newStatus = !isCurrentlyCompleted;

    updateAbyssFromOverview(characterId, newStatus);
}

// Update abyss status from overview
function updateAbyssFromOverview(characterId, completed) {
    // Get the abyss checkbox from the abyss tracker tab
    const abyssCheckbox = document.querySelector(`#abyss-tracker tr[data-character-id="${characterId}"] input[type="checkbox"]`);

    if (abyssCheckbox) {
        abyssCheckbox.checked = completed;

        // Trigger the existing abyss update function
        updateAbyss(characterId, completed);

        // Update the overview display
        updateOverviewAbyssStatus(characterId, completed);

        // Update overview summary stats
        updateOverviewStats();

        // Update filters if any are active
        updateFiltersOnStatusChange();
    } else {
        // If abyss tracker elements don't exist, make direct API call
        axios.post('/update_abyss', {
            character_id: characterId,
            completed: completed
        })
        .then(function (response) {
            if (response.data.success) {
                console.log('Abyss status updated successfully from overview');
                updateOverviewAbyssStatus(characterId, completed);
                updateOverviewStats();
                updateAbyssCount();
                updateFiltersOnStatusChange();
                showToast(`Abyss ${completed ? 'completed' : 'reset'} for character #${characterId}`);
            } else {
                console.error('Failed to update abyss status:', response.data.error);
                showToast('Error updating abyss status', 'error');
            }
        })
        .catch(function (error) {
            console.error('Error updating abyss status:', error);
            showToast('Error updating abyss status', 'error');
        });
    }
}

// Update overview void status display
function updateOverviewVoidStatus(characterId, voidNumber, completed) {
    const statusElement = document.getElementById(`overview-void${voidNumber}-${characterId}`);
    if (statusElement) {
        statusElement.textContent = completed ? 'Completed' : 'Incomplete';
        statusElement.className = `void-status ${completed ? 'completed' : 'incomplete'}`;
    }

    // Update the button text and style
    updateVoidButton(characterId, voidNumber, completed);
}

// Update void button appearance based on completion status
function updateVoidButton(characterId, voidNumber, completed) {
    const button = document.getElementById(`void${voidNumber}-btn-${characterId}`);
    if (button) {
        const icon = button.querySelector('i');
        if (completed) {
            button.className = 'action-btn void-toggle-btn reset';
            button.innerHTML = '<i class="fas fa-times"></i> Reset V' + voidNumber;
        } else {
            button.className = 'action-btn void-toggle-btn complete';
            button.innerHTML = '<i class="fas fa-check"></i> Complete V' + voidNumber;
        }
    }
}

// Update overview abyss status display
function updateOverviewAbyssStatus(characterId, completed) {
    const statusElement = document.getElementById(`overview-abyss-${characterId}`);
    if (statusElement) {
        statusElement.textContent = completed ? 'Completed' : 'Incomplete';
        statusElement.className = `abyss-status ${completed ? 'completed' : 'incomplete'}`;
    }

    // Update the button text and style
    updateAbyssButton(characterId, completed);
}

// Update abyss button appearance based on completion status
function updateAbyssButton(characterId, completed) {
    const button = document.getElementById(`abyss-btn-${characterId}`);
    if (button) {
        if (completed) {
            button.className = 'action-btn abyss-toggle-btn reset';
            button.innerHTML = '<i class="fas fa-times"></i> Reset Abyss';
        } else {
            button.className = 'action-btn abyss-toggle-btn complete';
            button.innerHTML = '<i class="fas fa-check"></i> Complete Abyss';
        }
    }
}

// Global variables
let currentEditingCharacterId = null;
let activeFilters = new Set(); // Track active filters

// Open accessory modal
function openAccessoryModal(characterId) {
    currentEditingCharacterId = characterId;

    // Get character data from the card
    const characterCard = document.querySelector(`[data-character-id="${characterId}"]`);
    if (!characterCard) {
        showToast('Character not found', 'error');
        return;
    }

    const characterIcon = characterCard.querySelector('.character-card-icon');
    const characterName = characterCard.querySelector('.character-card-name');

    // Set modal character info
    document.getElementById('modal-character-icon').src = characterIcon.src;
    document.getElementById('modal-character-icon').alt = characterName.textContent;
    document.getElementById('modal-character-name').textContent = characterName.textContent;

    // Get current accessory values from the overview card
    const ringIcon = document.getElementById(`overview-ring-icon-${characterId}`);
    const earringIcon = document.getElementById(`overview-earring-icon-${characterId}`);
    const piercingIcon = document.getElementById(`overview-piercing-icon-${characterId}`);

    const currentRing = ringIcon.title;
    const currentEarring = earringIcon.title;
    const currentPiercing = piercingIcon.title;

    // Set current values in modal selects
    document.getElementById('modal-ring-select').value = currentRing;
    document.getElementById('modal-earring-select').value = currentEarring;
    document.getElementById('modal-piercing-select').value = currentPiercing;

    // Update preview icons
    updateAccessoryPreview('ring', currentRing);
    updateAccessoryPreview('earring', currentEarring);
    updateAccessoryPreview('piercing', currentPiercing);

    // Show modal
    document.getElementById('accessory-modal').style.display = 'block';
}

// Close accessory modal
function closeAccessoryModal() {
    document.getElementById('accessory-modal').style.display = 'none';
    currentEditingCharacterId = null;
}

// Update accessory preview in modal
function updateAccessoryPreview(accessoryType, accessoryValue) {
    const previewIcon = document.getElementById(`modal-${accessoryType}-icon`);
    if (previewIcon) {
        const imagePath = `images/accessories/${accessoryType}s/${accessoryValue.replace(/ /g, '_')}.png`;
        previewIcon.src = `/static/${imagePath}`;
        previewIcon.alt = accessoryValue;
    }
}

// Save accessories from modal
function saveAccessories() {
    if (!currentEditingCharacterId) {
        showToast('No character selected', 'error');
        return;
    }

    const ringValue = document.getElementById('modal-ring-select').value;
    const earringValue = document.getElementById('modal-earring-select').value;
    const piercingValue = document.getElementById('modal-piercing-select').value;

    // Save each accessory
    const promises = [
        updateAccessoryFromOverview(currentEditingCharacterId, 'ring', ringValue),
        updateAccessoryFromOverview(currentEditingCharacterId, 'earring', earringValue),
        updateAccessoryFromOverview(currentEditingCharacterId, 'piercing', piercingValue)
    ];

    Promise.all(promises)
        .then(() => {
            showToast('Accessories updated successfully!', 'success');
            closeAccessoryModal();
        })
        .catch((error) => {
            console.error('Error updating accessories:', error);
            showToast('Error updating accessories', 'error');
        });
}

// Update accessory from overview
function updateAccessoryFromOverview(characterId, accessoryType, accessoryValue) {
    return axios.post('/update_accessory', {
        character_id: characterId,
        accessory_type: accessoryType,
        accessory_value: accessoryValue
    })
    .then(function (response) {
        if (response.data.success) {
            console.log(`${accessoryType} updated successfully from overview`);

            // Update the overview icon
            const overviewIcon = document.getElementById(`overview-${accessoryType}-icon-${characterId}`);
            if (overviewIcon) {
                const imagePath = `images/accessories/${accessoryType}s/${accessoryValue.replace(/ /g, '_')}.png`;
                overviewIcon.src = `/static/${imagePath}`;
                overviewIcon.alt = accessoryValue;
                overviewIcon.title = accessoryValue;
            }

            // Update the accessory tracker tab if it exists
            const accessorySelect = document.querySelector(`#accessory-tracker tr[data-character-id="${characterId}"] select[onchange*="${accessoryType}"]`);
            if (accessorySelect) {
                accessorySelect.value = accessoryValue;

                // Update the icon in the accessory tracker
                const accessoryTrackerIcon = document.getElementById(`${accessoryType}-icon-${characterId}`);
                if (accessoryTrackerIcon) {
                    const imagePath = `images/accessories/${accessoryType}s/${accessoryValue.replace(/ /g, '_')}.png`;
                    accessoryTrackerIcon.src = `/static/${imagePath}`;
                }
            }

            return response;
        } else {
            console.error(`Failed to update ${accessoryType}:`, response.data.error);
            throw new Error(`Failed to update ${accessoryType}`);
        }
    });
}

// Update overview summary statistics
function updateOverviewStats() {
    // Count void completions (only in overview tab to avoid counting other tabs)
    let voidCompletions = 0;
    document.querySelectorAll('#overview .void-status.completed').forEach(() => voidCompletions++);

    // Count abyss completions (only in overview tab)
    let abyssCompletions = 0;
    document.querySelectorAll('#overview .abyss-status.completed').forEach(() => abyssCompletions++);

    // Calculate average TA
    let totalTA = 0;
    let characterCount = 0;
    document.querySelectorAll('.ta-card-input').forEach(input => {
        totalTA += parseInt(input.value) || 0;
        characterCount++;
    });
    const avgTA = characterCount > 0 ? Math.round(totalTA / characterCount) : 0;

    // Update displays
    const voidCompletionsElement = document.getElementById('overview-void-completions');
    const abyssCompletionsElement = document.getElementById('overview-abyss-completions');
    const avgTAElement = document.getElementById('overview-avg-ta');

    if (voidCompletionsElement) voidCompletionsElement.textContent = voidCompletions;
    if (abyssCompletionsElement) abyssCompletionsElement.textContent = abyssCompletions;
    if (avgTAElement) avgTAElement.textContent = avgTA.toLocaleString();

    console.log(`Overview stats updated: Void: ${voidCompletions}, Abyss: ${abyssCompletions}, Avg TA: ${avgTA}`);
}

// Load overview data for all characters
function loadOverviewData() {
    console.log('Loading overview data for all characters');

    // Add a small delay to ensure DOM is fully rendered
    setTimeout(() => {
        updateOverviewStats();
    }, 100);

    // Only load dynamic data if needed (for real-time updates)
    // The initial data is already loaded from the template
}

// Load overview data for a specific character
function loadOverviewCharacterData(characterId) {
    // Load void data
    axios.get(`/get_void_data?character_id=${characterId}`)
    .then(function (response) {
        if (response.data.success) {
            updateOverviewVoidStatus(characterId, 1, response.data.void1);
            updateOverviewVoidStatus(characterId, 2, response.data.void2);
            updateOverviewVoidStatus(characterId, 3, response.data.void3);

            // Update shards display
            updateOverviewShards(characterId, {
                void1_shards: response.data.void1_shards || 0,
                void2_shards: response.data.void2_shards || 0,
                void3_shards: response.data.void3_shards || 0
            });
        }
    })
    .catch(function (error) {
        console.error(`Error loading void data for character ${characterId}:`, error);
    });

    // Load abyss data
    axios.get(`/get_abyss_data?character_id=${characterId}`)
    .then(function (response) {
        if (response.data.success) {
            updateOverviewAbyssStatus(characterId, response.data.completed);
        }
    })
    .catch(function (error) {
        console.error(`Error loading abyss data for character ${characterId}:`, error);
    });
}

// Update overview shards display
function updateOverviewShards(characterId, shards) {
    if (shards.void1_shards !== undefined) {
        const shards1Element = document.getElementById(`overview-shards1-${characterId}`);
        if (shards1Element) shards1Element.textContent = shards.void1_shards;
    }

    if (shards.void2_shards !== undefined) {
        const shards2Element = document.getElementById(`overview-shards2-${characterId}`);
        if (shards2Element) shards2Element.textContent = shards.void2_shards;
    }

    if (shards.void3_shards !== undefined) {
        const shards3Element = document.getElementById(`overview-shards3-${characterId}`);
        if (shards3Element) shards3Element.textContent = shards.void3_shards;
    }
}

// Update overview TA display
function updateOverviewTA(characterId, ta) {
    const taInput = document.getElementById(`overview-ta-${characterId}`);
    if (taInput) {
        taInput.value = ta;
    }
}

// Refresh overview data for a character (called from other tabs)
function refreshOverviewCharacter(characterId) {
    if (document.getElementById('overview').style.display === 'block') {
        loadOverviewCharacterData(characterId);
    }
}

// Filter Functions
function toggleFilter(filterType) {
    const filterBtn = document.getElementById(`filter-${filterType}`);

    if (activeFilters.has(filterType)) {
        // Remove filter
        activeFilters.delete(filterType);
        filterBtn.classList.remove('active');
    } else {
        // Add filter
        activeFilters.add(filterType);
        filterBtn.classList.add('active');
    }

    applyFilters();
}

function clearAllFilters() {
    activeFilters.clear();

    // Remove active class from all filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    applyFilters();
}

function applyFilters() {
    const characterCards = document.querySelectorAll('.character-card');

    characterCards.forEach(card => {
        const characterId = card.dataset.characterId;
        let shouldShow = true;

        if (activeFilters.size > 0) {
            // Check each active filter
            for (const filterType of activeFilters) {
                let isIncomplete = false;

                if (filterType === 'abyss') {
                    const abyssStatus = document.getElementById(`overview-abyss-${characterId}`);
                    isIncomplete = abyssStatus && !abyssStatus.classList.contains('completed');
                } else if (filterType.startsWith('void')) {
                    const voidNumber = filterType.replace('void', '');
                    const voidStatus = document.getElementById(`overview-void${voidNumber}-${characterId}`);
                    isIncomplete = voidStatus && !voidStatus.classList.contains('completed');
                }

                // If this filter requires incomplete and character is complete, hide the card
                if (!isIncomplete) {
                    shouldShow = false;
                    break;
                }
            }
        }

        // Apply visibility with animation
        if (shouldShow) {
            card.classList.remove('filtered-out');
            card.classList.add('filtered-in');
        } else {
            card.classList.remove('filtered-in');
            card.classList.add('filtered-out');
        }
    });

    // Update the total characters count to show filtered count
    updateFilteredCharacterCount();
}

function updateFilteredCharacterCount() {
    const totalChars = document.querySelectorAll('.character-card').length;
    const visibleChars = document.querySelectorAll('.character-card:not(.filtered-out)').length;
    const totalCharsElement = document.getElementById('overview-total-chars');

    if (activeFilters.size > 0) {
        totalCharsElement.textContent = `${visibleChars}/${totalChars}`;
        totalCharsElement.style.color = 'var(--primary-color)';
    } else {
        totalCharsElement.textContent = totalChars;
        totalCharsElement.style.color = '';
    }
}

// Update filters when completion status changes
function updateFiltersOnStatusChange() {
    if (activeFilters.size > 0) {
        // Re-apply filters after a short delay to allow DOM updates
        setTimeout(() => applyFilters(), 100);
    }
}

// Initialize overview when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Overview tab');

    // Load overview data when the page loads
    setTimeout(() => loadOverviewData(), 100);

    // Add click event listener to close modal when clicking outside
    const modal = document.getElementById('accessory-modal');
    if (modal) {
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                closeAccessoryModal();
            }
        });
    }
});
