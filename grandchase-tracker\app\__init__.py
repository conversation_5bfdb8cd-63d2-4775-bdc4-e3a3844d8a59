"""
GrandChase Tracker - Flask Application Initialization
"""
import os
from flask import Flask

def create_app(db_path=None):
    """Create and configure the Flask application"""
    app = Flask(__name__)
    
    # Load configuration
    from config import config
    app.config.from_object(config)
    
    # Override database path if provided
    if db_path:
        app.config['DATABASE_PATH'] = db_path
    
    # Ensure the database directory exists
    os.makedirs(os.path.dirname(os.path.abspath(app.config['DATABASE_PATH'])), exist_ok=True)
    
    # Initialize database
    from app.models.database import init_db, init_characters
    with app.app_context():
        init_db(app.config['DATABASE_PATH'])
        init_characters(app.config['DATABASE_PATH'], app.config['CHARACTERS'])
    
    # Register blueprints
    from app.routes.void import void_bp
    from app.routes.ta import ta_bp
    from app.routes.gear import gear_bp
    from app.routes.abyss import abyss_bp
    from app.routes.tod import tod_bp
    from app.routes.accessory import accessory_bp
    
    app.register_blueprint(void_bp)
    app.register_blueprint(ta_bp)
    app.register_blueprint(gear_bp)
    app.register_blueprint(abyss_bp)
    app.register_blueprint(tod_bp)
    app.register_blueprint(accessory_bp)
    
    # Register main route
    from app.routes import main_bp
    app.register_blueprint(main_bp)
    
    return app
