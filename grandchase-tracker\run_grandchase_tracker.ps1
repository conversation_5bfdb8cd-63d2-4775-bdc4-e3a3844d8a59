# GrandChase Tracker Launcher
$host.UI.RawUI.WindowTitle = "GrandChase Tracker"

# Function to display colored text
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

Clear-Host
Write-ColorOutput "Blue" "`n ========================================"
Write-ColorOutput "Cyan" "   GrandChase Tracker - Launcher"
Write-ColorOutput "Blue" " ========================================`n"
Write-Output " Starting application...`n"

# Change to the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    if (-not $pythonVersion) {
        throw "Python not found"
    }
}
catch {
    Write-ColorOutput "Red" " ERROR: Python is not installed or not in PATH."
    Write-ColorOutput "Red" " Please install Python and try again.`n"
    Read-Host " Press Enter to exit"
    exit 1
}

# Check if main.py exists
if (-not (Test-Path "main.py")) {
    Write-ColorOutput "Red" " ERROR: main.py not found in the current directory."
    Write-ColorOutput "Red" " Please make sure you're running this from the correct location.`n"
    Read-Host " Press Enter to exit"
    exit 1
}

# Run the application
Write-Output " Server starting at http://localhost:5000"
Write-Output " The application will open in your default browser."
Write-Output "`n To close the application, close this window or press Ctrl+C."
Write-ColorOutput "Blue" "`n ========================================`n"

try {
    python main.py
}
catch {
    Write-ColorOutput "Red" " ERROR: Failed to start the application."
    Write-ColorOutput "Red" " Error details: $_`n"
}

# If we get here, the application has stopped
Write-Output "`n Application has stopped."
Read-Host " Press Enter to exit"
