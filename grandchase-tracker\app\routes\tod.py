"""
GrandChase Tracker - Tower of Disappearance (ToD) Routes
"""
import json
from datetime import datetime, timedelta, timezone
from flask import Blueprint, request, jsonify, current_app
from app.models.database import get_db_connection

tod_bp = Blueprint('tod', __name__)

@tod_bp.route('/update_tod', methods=['POST'])
def update_tod():
    """Update ToD status for a character"""
    data = request.get_json()
    character_id = data.get('character_id')
    cleared = data.get('cleared')
    spr_dropped = data.get('spr_dropped', 0)
    floor = data.get('floor', 10)
    detailed_data = data.get('detailed_data')

    # Convert detailed_data to JSON string if it exists
    detailed_data_json = json.dumps(detailed_data) if detailed_data else None

    # Get today's date
    now = datetime.now(timezone.utc)
    reset_hour = current_app.config.get('RESET_HOUR_UTC', 6)
    reset_time_utc = now.replace(hour=reset_hour, minute=0, second=0, microsecond=0)

    # Determine the date based on the reset time
    if now < reset_time_utc:
        today = (now - timedelta(days=1)).strftime('%Y-%m-%d')
    else:
        today = now.strftime('%Y-%m-%d')

    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Check if there's an entry for today
            cursor.execute('''
                SELECT id FROM tod_runs
                WHERE character_id = ? AND date = ?
            ''', (character_id, today))

            result = cursor.fetchone()

            if result:
                # Update existing entry
                cursor.execute('''
                    UPDATE tod_runs
                    SET cleared = ?, spr_dropped = ?, floor = ?, detailed_data = ?
                    WHERE character_id = ? AND date = ?
                ''', (cleared, spr_dropped, floor, detailed_data_json, character_id, today))
            else:
                # Create new entry
                cursor.execute('''
                    INSERT INTO tod_runs
                    (character_id, cleared, spr_dropped, floor, date, detailed_data)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (character_id, cleared, spr_dropped, floor, today, detailed_data_json))

            conn.commit()

            return jsonify(success=True)
    except Exception as e:
        return jsonify(success=False, error=str(e))

@tod_bp.route('/get_tod_count', methods=['GET'])
def get_tod_count():
    """Get count of completed ToD runs for today"""
    try:
        # Get the current time in UTC
        now = datetime.now(timezone.utc)
        reset_hour = current_app.config.get('RESET_HOUR_UTC', 6)
        reset_time_utc = now.replace(hour=reset_hour, minute=0, second=0, microsecond=0)

        # Determine the date based on the reset time
        if now < reset_time_utc:
            today = (now - timedelta(days=1)).strftime('%Y-%m-%d')
        else:
            today = now.strftime('%Y-%m-%d')

        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Get total character count
            cursor.execute('SELECT COUNT(*) as total FROM characters')
            total_characters = cursor.fetchone()['total']

            # Get completed ToD count for today
            cursor.execute('''
                SELECT COUNT(*) as completed
                FROM tod_runs
                WHERE date = ? AND cleared = 1
            ''', (today,))

            completed_count = cursor.fetchone()['completed'] or 0

            return jsonify(
                success=True,
                completed_count=completed_count,
                total_characters=total_characters
            )
    except Exception as e:
        return jsonify(success=False, error=str(e))

@tod_bp.route('/get_tod_data', methods=['GET'])
def get_tod_data():
    """Get ToD data for a specific character and date"""
    character_id = request.args.get('character_id')
    date = request.args.get('date')

    if not character_id or not date:
        return jsonify(success=False, error="Missing character_id or date parameter")

    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Get ToD data
            cursor.execute('''
                SELECT cleared, spr_dropped, floor, detailed_data
                FROM tod_runs
                WHERE character_id = ? AND date = ?
            ''', (character_id, date))

            result = cursor.fetchone()

            if result:
                # Parse detailed_data JSON if it exists
                detailed_data = None
                if result['detailed_data']:
                    try:
                        detailed_data = json.loads(result['detailed_data'])
                    except json.JSONDecodeError:
                        detailed_data = None

                return jsonify(
                    success=True,
                    cleared=bool(result['cleared']),
                    spr_dropped=result['spr_dropped'],
                    floor=result['floor'],
                    detailed_data=detailed_data
                )
            else:
                # No data found for this character on this date, return default values
                return jsonify(
                    success=True,
                    cleared=False,
                    spr_dropped=0,
                    floor=10,  # Default to 10F
                    detailed_data=None
                )
    except Exception as e:
        return jsonify(success=False, error=str(e))

@tod_bp.route('/get_tod_history', methods=['GET'])
def get_tod_history():
    """Get ToD history for a date range"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    if not start_date or not end_date:
        return jsonify(success=False, error="Missing start_date or end_date parameter")

    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Get ToD history data with detailed information
            cursor.execute('''
                SELECT
                    date,
                    detailed_data,
                    floor,
                    cleared,
                    spr_dropped
                FROM tod_runs
                WHERE date BETWEEN ? AND ?
                ORDER BY date
            ''', (start_date, end_date))

            history_data = cursor.fetchall()

            # Process the history data with detailed drop information
            daily_data = {}

            # Initialize counters
            total_floor5_chars = 0  # Characters that cleared 5F
            total_floor10_chars = 0  # Characters that cleared 10F
            total_floor5_runs = 0  # Total runs in 5F (3 per character)
            total_floor10_runs = 0  # Total runs in 10F (3 per character)
            total_floor5_spr_from_5f = 0  # 5F drops from 5F runs
            total_floor5_spr_from_10f = 0  # 5F drops from 10F runs
            total_floor10_spr = 0  # 10F drops

            for entry in history_data:
                date = entry['date']
                floor = entry['floor']
                cleared = entry['cleared']
                spr_dropped = entry['spr_dropped'] or 0
                detailed_data_json = entry['detailed_data']

                if not cleared:
                    continue  # Skip uncleared runs

                # Initialize the date entry if it doesn't exist
                if date not in daily_data:
                    daily_data[date] = {
                        'floor5_chars': 0,  # Characters that cleared 5F
                        'floor10_chars': 0,  # Characters that cleared 10F
                        'floor5_runs': 0,  # Total runs in 5F (3 per character)
                        'floor10_runs': 0,  # Total runs in 10F (3 per character)
                        'floor5_spr_from_5f': 0,
                        'floor5_spr_from_10f': 0,
                        'floor10_spr': 0
                    }

                # Count the character and runs based on floor (3 runs per character)
                if floor == 5:
                    daily_data[date]['floor5_chars'] += 1
                    daily_data[date]['floor5_runs'] += 3  # 3 runs per character
                    total_floor5_chars += 1
                    total_floor5_runs += 3
                elif floor == 10:
                    daily_data[date]['floor10_chars'] += 1
                    daily_data[date]['floor10_runs'] += 3  # 3 runs per character
                    total_floor10_chars += 1
                    total_floor10_runs += 3

                # Process detailed drop data if available
                if detailed_data_json:
                    try:
                        detailed_data = json.loads(detailed_data_json)

                        # New format with separate 5F and 10F toggles
                        if 'run1_5f' in detailed_data:
                            # Count 5F drops
                            floor5_drops = (
                                (1 if detailed_data.get('run1_5f') else 0) +
                                (1 if detailed_data.get('run2_5f') else 0) +
                                (1 if detailed_data.get('run3_5f') else 0)
                            )

                            # Count 10F drops
                            floor10_drops = (
                                (1 if detailed_data.get('run1_10f') else 0) +
                                (1 if detailed_data.get('run2_10f') else 0) +
                                (1 if detailed_data.get('run3_10f') else 0)
                            )

                            if floor == 5:
                                daily_data[date]['floor5_spr_from_5f'] += floor5_drops
                                total_floor5_spr_from_5f += floor5_drops
                            elif floor == 10:
                                daily_data[date]['floor5_spr_from_10f'] += floor5_drops
                                daily_data[date]['floor10_spr'] += floor10_drops
                                total_floor5_spr_from_10f += floor5_drops
                                total_floor10_spr += floor10_drops
                        else:
                            # Legacy format - all drops counted based on floor
                            if floor == 5:
                                daily_data[date]['floor5_spr_from_5f'] += spr_dropped
                                total_floor5_spr_from_5f += spr_dropped
                            elif floor == 10:
                                daily_data[date]['floor10_spr'] += spr_dropped
                                total_floor10_spr += spr_dropped
                    except json.JSONDecodeError:
                        # Fallback to simple counting if JSON is invalid
                        if floor == 5:
                            daily_data[date]['floor5_spr_from_5f'] += spr_dropped
                            total_floor5_spr_from_5f += spr_dropped
                        elif floor == 10:
                            daily_data[date]['floor10_spr'] += spr_dropped
                            total_floor10_spr += spr_dropped
                else:
                    # No detailed data - use simple counting
                    if floor == 5:
                        daily_data[date]['floor5_spr_from_5f'] += spr_dropped
                        total_floor5_spr_from_5f += spr_dropped
                    elif floor == 10:
                        daily_data[date]['floor10_spr'] += spr_dropped
                        total_floor10_spr += spr_dropped

            # Format the history data
            formatted_history = []
            for date, data in daily_data.items():
                floor5_chars = data['floor5_chars']
                floor10_chars = data['floor10_chars']
                floor5_runs = data['floor5_runs']
                floor10_runs = data['floor10_runs']
                floor5_spr_from_5f = data['floor5_spr_from_5f']
                floor5_spr_from_10f = data['floor5_spr_from_10f']
                floor10_spr = data['floor10_spr']

                # Calculate drop rates (based on total runs, not characters)
                floor5_rate_5f = (floor5_spr_from_5f / floor5_runs * 100) if floor5_runs > 0 else 0
                floor5_rate_10f = (floor5_spr_from_10f / floor10_runs * 100) if floor10_runs > 0 else 0
                floor10_rate = (floor10_spr / floor10_runs * 100) if floor10_runs > 0 else 0

                formatted_history.append({
                    'date': date,
                    'floor5_chars': floor5_chars,
                    'floor10_chars': floor10_chars,
                    'floor5_runs': floor5_runs,
                    'floor10_runs': floor10_runs,
                    'floor5_spr_from_5f': floor5_spr_from_5f,
                    'floor5_spr_from_10f': floor5_spr_from_10f,
                    'floor10_spr': floor10_spr,
                    'floor5_rate_5f': round(floor5_rate_5f, 2),
                    'floor5_rate_10f': round(floor5_rate_10f, 2),
                    'floor10_rate': round(floor10_rate, 2)
                })

            # Calculate overall drop rates (based on total runs, not characters)
            floor5_rate_5f = (total_floor5_spr_from_5f / total_floor5_runs * 100) if total_floor5_runs > 0 else 0
            floor5_rate_10f = (total_floor5_spr_from_10f / total_floor10_runs * 100) if total_floor10_runs > 0 else 0
            floor10_rate = (total_floor10_spr / total_floor10_runs * 100) if total_floor10_runs > 0 else 0

            # Total SPR values
            total_spr = total_floor5_spr_from_5f + total_floor5_spr_from_10f + total_floor10_spr

            return jsonify(
                success=True,
                history=formatted_history,
                totals={
                    'floor5_chars': total_floor5_chars,  # Characters that cleared 5F
                    'floor10_chars': total_floor10_chars,  # Characters that cleared 10F
                    'floor5_runs': total_floor5_runs,  # Total runs in 5F (3 per character)
                    'floor10_runs': total_floor10_runs,  # Total runs in 10F (3 per character)
                    'floor5_spr_from_5f': total_floor5_spr_from_5f,
                    'floor5_spr_from_10f': total_floor5_spr_from_10f,
                    'floor10_spr': total_floor10_spr,
                    'total_spr': total_spr,
                    'floor5_rate_5f': round(floor5_rate_5f, 2),
                    'floor5_rate_10f': round(floor5_rate_10f, 2),
                    'floor10_rate': round(floor10_rate, 2)
                }
            )
    except Exception as e:
        return jsonify(success=False, error=str(e))





