@echo off
title GrandChase Tracker
color 0B

echo.
echo  ========================================
echo    GrandChase Tracker - Launcher
echo  ========================================
echo.
echo  Starting application...
echo.

:: Change to the directory where the batch file is located
cd /d "%~dp0"

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    color 0C
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python and try again.
    echo.
    pause
    exit /b 1
)

:: Check if main.py exists
if not exist main.py (
    color 0C
    echo ERROR: main.py not found in the current directory.
    echo Please make sure you're running this from the correct location.
    echo.
    pause
    exit /b 1
)

:: Run the application
echo  Server starting at http://localhost:5000
echo  The application will open in your default browser.
echo.
echo  To close the application, close this window or press Ctrl+C.
echo.
echo  ========================================
echo.

python main.py

:: If we get here, the application has stopped
echo.
echo  Application has stopped.
pause
