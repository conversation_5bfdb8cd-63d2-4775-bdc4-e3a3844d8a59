@echo off
title Testing ToD Tracker Fix (Version 2)
color 0B

echo.
echo  ========================================
echo    Testing ToD Tracker Fix (Version 2)
echo  ========================================
echo.
echo  This script will run the application to test the improved fix for:
echo  - ToD Tracker checkbox status not being saved on refresh
echo.
echo  Instructions:
echo  1. Go to the ToD Tracker tab
echo  2. Check some "Cleared" checkboxes
echo  3. Refresh the page
echo  4. Verify that the checkbox states are preserved
echo  5. Try switching to another tab and back to ToD Tracker
echo  6. Verify that the checkbox states are still preserved
echo.
echo  Changes in this version:
echo  - Added direct initialization of ToD data on page load
echo  - Added global flag to track if ToD data has been loaded
echo  - Improved error handling and logging
echo.
echo  Press any key to start the application...
pause > nul

:: Change to the directory where the batch file is located
cd /d "%~dp0"

:: Run the application
python main.py

echo.
echo  Application has stopped.
echo  Did the fix work? If not, please let me know what issues remain.
echo.
pause
