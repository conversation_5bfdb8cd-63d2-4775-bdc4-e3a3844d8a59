/**
 * GrandChase Tracker - TA JavaScript
 * Contains functionality for the TA Tracker tab
 */

// Update TA score for a character
function updateTA(characterId) {
    const taInput = document.getElementById(`ta-input-${characterId}`);
    const ta = parseInt(taInput.value);

    axios.post('/update_ta', {
        character_id: characterId,
        ta: ta
    })
    .then(function (response) {
        if (response.data.success) {
            console.log('TA updated successfully');
            showToast(`Updated TA for character #${characterId}`);
        } else {
            console.error('Failed to update TA:', response.data.error);
            showToast('Error updating TA', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error updating TA:', error);
        showToast('Error updating TA', 'error');
    });
}

// Save TA tier list settings
function saveTASettings() {
    // Get default position
    const defaultPositionRadios = document.getElementsByName('default-ta-tier-list-position');
    let defaultPosition = 'below-tabs';
    for (const radio of defaultPositionRadios) {
        if (radio.checked) {
            defaultPosition = radio.value;
            break;
        }
    }

    // Get selected tabs with their individual positions
    const tabCheckboxes = document.querySelectorAll('.tab-setting-header input[type="checkbox"]');
    const tabSettings = {};

    tabCheckboxes.forEach(checkbox => {
        const tabId = checkbox.value;
        if (checkbox.checked) {
            // Get position for this specific tab
            const tabPositionRadios = document.getElementsByName(`position-${tabId}`);
            let tabPosition = defaultPosition;

            for (const radio of tabPositionRadios) {
                if (radio.checked) {
                    tabPosition = radio.value;
                    break;
                }
            }

            tabSettings[tabId] = {
                enabled: true,
                position: tabPosition
            };
        }
    });

    // Save settings to localStorage
    const settings = {
        defaultPosition: defaultPosition,
        tabSettings: tabSettings
    };

    localStorage.setItem('ta_tier_list_settings', JSON.stringify(settings));

    // Apply settings
    applyTASettings();

    showToast('TA Tier List settings saved');
}

// Load TA tier list settings
function loadTASettings() {
    const settingsJson = localStorage.getItem('ta_tier_list_settings');

    if (settingsJson) {
        const settings = JSON.parse(settingsJson);

        // Set default position
        const defaultPosition = settings.defaultPosition || settings.position || 'below-tabs';
        const defaultPositionRadio = document.getElementById(`default-position-${defaultPosition}`);
        if (defaultPositionRadio) {
            defaultPositionRadio.checked = true;
        }

        // Uncheck all tab checkboxes first
        const tabCheckboxes = document.querySelectorAll('.tab-setting-header input[type="checkbox"]');
        tabCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // Handle both old and new format
        if (settings.tabs) {
            // Old format - just tab IDs
            settings.tabs.forEach(tabId => {
                const checkbox = document.getElementById(`tab-${tabId}`);
                if (checkbox) {
                    checkbox.checked = true;
                }

                // Set default position for all tabs
                const tabPositionRadio = document.getElementById(`position-${tabId}-${defaultPosition}`);
                if (tabPositionRadio) {
                    tabPositionRadio.checked = true;
                }
            });

            // Convert to new format for future use
            const tabSettings = {};
            settings.tabs.forEach(tabId => {
                tabSettings[tabId] = {
                    enabled: true,
                    position: defaultPosition
                };
            });
            settings.tabSettings = tabSettings;
            settings.defaultPosition = defaultPosition;
            delete settings.tabs;
            delete settings.position;
        } else if (settings.tabSettings) {
            // New format - object with tab settings
            Object.keys(settings.tabSettings).forEach(tabId => {
                const tabSetting = settings.tabSettings[tabId];
                if (tabSetting.enabled) {
                    // Set checkbox
                    const checkbox = document.getElementById(`tab-${tabId}`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }

                    // Set position radio
                    const tabPosition = tabSetting.position || defaultPosition;
                    const tabPositionRadio = document.getElementById(`position-${tabId}-${tabPosition}`);
                    if (tabPositionRadio) {
                        tabPositionRadio.checked = true;
                    }
                }
            });
        }

        return settings;
    }

    // Default settings
    return {
        defaultPosition: 'below-tabs',
        tabSettings: {
            'ta-tracker': {
                enabled: true,
                position: 'below-tabs'
            }
        }
    };
}

// Apply TA tier list settings
function applyTASettings() {
    const settings = loadTASettings();
    const globalTierList = document.getElementById('global-ta-tier-list');

    // Get the active tab
    const activeTab = document.querySelector('.tab-content:not([style*="display: none"])');
    if (!activeTab) {
        globalTierList.style.display = 'none';
        return;
    }

    const activeTabId = activeTab.id;

    // Check if tier list should be shown for this tab
    let showTierList = false;
    let position = settings.defaultPosition || 'below-tabs';

    if (settings.tabSettings && settings.tabSettings[activeTabId]) {
        showTierList = settings.tabSettings[activeTabId].enabled;
        position = settings.tabSettings[activeTabId].position || position;
    } else if (settings.tabs && settings.tabs.includes(activeTabId)) {
        // Backward compatibility
        showTierList = true;
    }

    if (!showTierList) {
        globalTierList.style.display = 'none';
        return;
    }

    // Apply position based on the tab's setting
    if (position === 'below-tabs') {
        // Move below tabs
        const tabsContainer = document.querySelector('.tabs');
        if (tabsContainer) {
            const container = document.querySelector('.container');
            container.insertBefore(globalTierList, tabsContainer.nextSibling);
        }
    } else {
        // Move to bottom
        const container = document.querySelector('.container');
        container.appendChild(globalTierList);
    }

    // Show the tier list
    globalTierList.style.display = 'block';
}

// Toggle TA tier list display
function toggleTATierList() {
    const globalTierList = document.getElementById('global-ta-tier-list');

    if (globalTierList.style.display === 'none') {
        axios.get('/get_ta_tier_list')
            .then(function (response) {
                if (response.data.success) {
                    renderTATierList(response.data.tier_list);
                    globalTierList.style.display = 'block';

                    // Calculate and display average TA
                    const averageTA = calculateAverageTA(response.data.tier_list);
                    document.getElementById('average-ta-display').textContent = `Average TA: ${averageTA.toLocaleString()}`;
                } else {
                    console.error('Failed to get TA tier list:', response.data.error);
                    showToast('Error loading TA tier list', 'error');
                }
            })
            .catch(function (error) {
                console.error('Error getting TA tier list:', error);
                showToast('Error loading TA tier list', 'error');
            });
    } else {
        globalTierList.style.display = 'none';
    }
}

// Render TA tier list
function renderTATierList(tierList) {
    const tierListContainer = document.getElementById('ta-tier-list');
    tierListContainer.innerHTML = '';

    // Sort the entire tier list by TA score in descending order
    const sortedTierList = [...tierList].sort((a, b) => b.ta - a.ta);

    // Add title
    const titleContainer = document.createElement('div');
    titleContainer.className = 'ta-tier-title';

    const trophyIcon = document.createElement('i');
    trophyIcon.className = 'fas fa-trophy';

    const titleText = document.createElement('span');
    titleText.textContent = 'TA Tier List';

    titleContainer.appendChild(trophyIcon);
    titleContainer.appendChild(titleText);
    tierListContainer.appendChild(titleContainer);

    // Group characters by 100k TA tiers
    const tierGroups = {};
    const tierValues = {}; // Store numeric values for sorting

    sortedTierList.forEach(character => {
        // Calculate tier (e.g., 1.2M+, 1.1M+, etc.)
        const tierValue = Math.floor(character.ta / 100000) * 100000;
        let tierKey;

        if (tierValue >= 1000000) {
            // Format as X.YM+ for values >= 1M
            const millions = tierValue / 1000000;
            tierKey = `${millions.toFixed(1)}M+`;
        } else {
            // Format as XXXk+ for values < 1M
            tierKey = `${tierValue / 1000}k+`;
        }

        if (!tierGroups[tierKey]) {
            tierGroups[tierKey] = [];
            tierValues[tierKey] = tierValue; // Store the actual numeric value
        }

        tierGroups[tierKey].push(character);
    });

    // Sort tiers in descending order using the actual numeric values
    const sortedTiers = Object.keys(tierGroups).sort((a, b) => {
        return tierValues[b] - tierValues[a];
    });

    // Create tier list container
    const tiersContainer = document.createElement('div');
    tiersContainer.className = 'ta-tiers-container';

    // Define tier colors
    const tierColors = {
        '2.0M+': '#FF5252', // Red
        '1.9M+': '#FF7043', // Deep Orange
        '1.8M+': '#FFA726', // Orange
        '1.7M+': '#FFCA28', // Amber
        '1.6M+': '#FFEE58', // Yellow
        '1.5M+': '#D4E157', // Lime
        '1.4M+': '#9CCC65', // Light Green
        '1.3M+': '#66BB6A', // Green
        '1.2M+': '#26A69A', // Teal
        '1.1M+': '#29B6F6', // Light Blue
        '1.0M+': '#42A5F5', // Blue
        '900k+': '#5C6BC0', // Indigo
        '800k+': '#7E57C2', // Deep Purple
        '700k+': '#AB47BC', // Purple
        '600k+': '#EC407A', // Pink
        '500k+': '#EF5350', // Red (lighter)
        '400k+': '#FF7043', // Deep Orange (lighter)
        '300k+': '#FFA726', // Orange (lighter)
        '200k+': '#FFCA28', // Amber (lighter)
        '100k+': '#FFEE58', // Yellow (lighter)
        '0k+': '#D4E157'    // Lime (lighter)
    };

    // Helper function to determine if text should be black or white based on background brightness
    function getBrightness(hexColor) {
        // Remove # if present
        hexColor = hexColor.replace('#', '');

        // Convert to RGB
        const r = parseInt(hexColor.substr(0, 2), 16);
        const g = parseInt(hexColor.substr(2, 2), 16);
        const b = parseInt(hexColor.substr(4, 2), 16);

        // Calculate brightness using the formula (0.299*R + 0.587*G + 0.114*B)
        return (0.299 * r + 0.587 * g + 0.114 * b);
    }

    // Create rows for each tier
    sortedTiers.forEach(tier => {
        const tierRow = document.createElement('div');
        tierRow.className = 'ta-tier-row';

        // Get color for this tier or use a default
        const tierColor = tierColors[tier] || '#78909C'; // Default to blue-grey

        // Create tier label
        const tierLabel = document.createElement('div');
        tierLabel.className = 'ta-tier-label';
        tierLabel.textContent = tier;
        tierLabel.style.backgroundColor = tierColor;
        tierLabel.style.color = getBrightness(tierColor) > 160 ? '#000' : '#fff';
        tierRow.appendChild(tierLabel);

        // Characters are already sorted by TA due to the initial sort
        const charactersInTier = tierGroups[tier];

        // Create character list for this tier
        const characterList = document.createElement('div');
        characterList.className = 'ta-character-list';
        characterList.style.borderColor = tierColor;

        charactersInTier.forEach(character => {
            // Create character item
            const characterItem = document.createElement('div');
            characterItem.className = 'ta-character-item';

            // Create character icon
            const characterIcon = document.createElement('img');
            characterIcon.src = `/static/${character.image_path}`;
            characterIcon.alt = character.name;
            characterIcon.className = 'character-icon';
            characterItem.appendChild(characterIcon);

            // Create character name
            const characterName = document.createElement('span');
            characterName.className = 'ta-character-name';
            characterName.textContent = character.name;
            characterItem.appendChild(characterName);

            // Create character TA
            const characterTA = document.createElement('span');
            characterTA.className = 'ta-character-score';
            characterTA.textContent = `(${character.ta.toLocaleString()})`;
            characterItem.appendChild(characterTA);

            characterList.appendChild(characterItem);
        });

        tierRow.appendChild(characterList);
        tiersContainer.appendChild(tierRow);
    });

    tierListContainer.appendChild(tiersContainer);
}

// Calculate average TA
function calculateAverageTA(tierList) {
    if (!tierList || tierList.length === 0) {
        return 0;
    }

    const sum = tierList.reduce((total, character) => total + character.ta, 0);
    const average = Math.round(sum / tierList.length);

    return average;
}

// Add event listeners and initialize TA settings
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all TA inputs
    const taInputs = document.querySelectorAll('.ta-input');

    taInputs.forEach(input => {
        input.addEventListener('keypress', function(event) {
            // If Enter key is pressed
            if (event.key === 'Enter') {
                event.preventDefault();

                // Get the character ID from the input ID
                const characterId = this.id.split('-')[2];

                // Call the updateTA function
                updateTA(characterId);
            }
        });
    });

    // Load TA settings
    loadTASettings();

    // Load TA tier list on page load
    axios.get('/get_ta_tier_list')
        .then(function (response) {
            if (response.data.success) {
                renderTATierList(response.data.tier_list);

                // Calculate and display average TA
                const averageTA = calculateAverageTA(response.data.tier_list);
                document.getElementById('average-ta-display').textContent = `Average TA: ${averageTA.toLocaleString()}`;

                // Apply settings
                applyTASettings();
            }
        })
        .catch(function (error) {
            console.error('Error getting TA tier list:', error);
        });
});
