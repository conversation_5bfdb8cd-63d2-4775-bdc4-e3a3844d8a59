===================================
GrandChase Tracker - Launcher Files
===================================

This folder contains several launcher files to help you start the GrandChase Tracker application easily.

Available Launchers:
-------------------

1. run_grandchase_tracker.bat
   - Simple batch file launcher
   - Double-click to run

2. run_grandchase_tracker_advanced.bat
   - Advanced batch file with error handling and better visuals
   - Double-click to run

3. run_grandchase_tracker.ps1
   - PowerShell launcher with nice formatting
   - Right-click and select "Run with PowerShell" to execute

Creating a Desktop Shortcut:
---------------------------

To create a desktop shortcut:

1. Right-click on one of the .bat files (recommended: run_grandchase_tracker_advanced.bat)
2. Select "Create shortcut"
3. Move the shortcut to your desktop or preferred location

For the PowerShell script:
1. Right-click on run_grandchase_tracker.ps1
2. Select "Create shortcut"
3. Right-click on the created shortcut and select "Properties"
4. In the "Target" field, change it to:
   powershell.exe -ExecutionPolicy Bypass -File "C:\path\to\run_grandchase_tracker.ps1"
   (Replace C:\path\to with the actual path to the script)
5. Click "OK" and move the shortcut to your desktop

Notes:
-----
- All launchers will start the application and open it in your default browser
- To close the application, close the command window that opens
- Make sure Python is installed and in your PATH
- These launchers must be in the same directory as main.py
