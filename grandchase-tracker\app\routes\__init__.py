"""
GrandChase Tracker - Main Routes
"""
import os
from datetime import datetime, timedelta, timezone
from flask import Blueprint, render_template, send_from_directory, current_app
from app.models.database import get_db_connection

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Render the main page"""
    # Get the current time in UTC
    now = datetime.now(timezone.utc)
    
    # Set the reset time to the configured hour in UTC
    reset_hour = current_app.config.get('RESET_HOUR_UTC', 6)
    reset_time_utc = now.replace(hour=reset_hour, minute=0, second=0, microsecond=0)
    
    # Determine the date based on the reset time
    if now < reset_time_utc:
        today = (now - timedelta(days=1)).strftime('%Y-%m-%d')
    else:
        today = now.strftime('%Y-%m-%d')
    
    # Get database connection
    db_path = current_app.config['DATABASE_PATH']
    
    with get_db_connection(db_path) as conn:
        cursor = conn.cursor()
        
        # Fetch characters and their data
        cursor.execute('''
            SELECT 
                c.id, c.name, c.image_path,
                si.void1_shards, si.void2_shards, si.void3_shards,
                COALESCE(vr.void1, 0) as void1,
                COALESCE(vr.void2, 0) as void2,
                COALESCE(vr.void3, 0) as void3,
                COALESCE(vr.void1_clear_level, 3) as void1_clear_level,
                COALESCE(vr.void2_clear_level, 3) as void2_clear_level,
                COALESCE(vr.void3_clear_level, 3) as void3_clear_level,
                ts.ta_score,
                g.weapon, g.helmet, g.armor, g.pants, g.gloves, g.shoes, g.cloak,
                COALESCE(ar.completed, 0) as abyss_completed,
                COALESCE(tr.cleared, 0) as tod_cleared,
                COALESCE(tr.spr_dropped, 0) as tod_spr_dropped,
                COALESCE(tr.floor, 10) as tod_floor,
                ac.ring, ac.earring, ac.piercing
            FROM characters c
            LEFT JOIN shard_inventory si ON c.id = si.character_id
            LEFT JOIN (
                SELECT character_id, void1, void2, void3, void1_clear_level, void2_clear_level, void3_clear_level
                FROM void_raids
                WHERE date = ?
            ) vr ON c.id = vr.character_id
            LEFT JOIN ta_scores ts ON c.id = ts.character_id
            LEFT JOIN gear g ON c.id = g.character_id
            LEFT JOIN abyss_runs ar ON c.id = ar.character_id
            LEFT JOIN (
                SELECT character_id, cleared, spr_dropped, floor
                FROM tod_runs
                WHERE date = ?
            ) tr ON c.id = tr.character_id
            LEFT JOIN accessories ac ON c.id = ac.character_id
            ORDER BY c.id
        ''', (today, today))
        
        characters = cursor.fetchall()
    
    return render_template('index.html', characters=characters, today=today)

@main_bp.route('/favicon.ico')
def favicon():
    """Serve the favicon"""
    return send_from_directory(
        os.path.join(current_app.root_path, 'static'),
        'favicon.ico',
        mimetype='image/vnd.microsoft.icon'
    )
