"""
GrandChase Tracker - TA Routes
"""
from flask import Blueprint, request, jsonify, current_app
from app.models.database import get_db_connection

ta_bp = Blueprint('ta', __name__)

@ta_bp.route('/update_ta', methods=['POST'])
def update_ta():
    """Update TA score for a character"""
    data = request.get_json()
    character_id = data.get('character_id')
    ta_score = data.get('ta')
    
    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()
            
            # Update TA score
            cursor.execute('''
                UPDATE ta_scores
                SET ta_score = ?
                WHERE character_id = ?
            ''', (ta_score, character_id))
            
            # If no rows were updated, insert a new record
            if cursor.rowcount == 0:
                cursor.execute('''
                    INSERT INTO ta_scores (character_id, ta_score)
                    VALUES (?, ?)
                ''', (character_id, ta_score))
            
            conn.commit()
            
            return jsonify(success=True)
    except Exception as e:
        return jsonify(success=False, error=str(e))

@ta_bp.route('/get_ta_tier_list', methods=['GET'])
def get_ta_tier_list():
    """Get TA tier list sorted by TA score"""
    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()
            
            # Get characters sorted by TA score
            cursor.execute('''
                SELECT c.id, c.name, c.image_path, ts.ta_score
                FROM characters c
                JOIN ta_scores ts ON c.id = ts.character_id
                ORDER BY ts.ta_score DESC
            ''')
            
            characters = cursor.fetchall()
            
            # Format the tier list
            tier_list = [
                {
                    'id': char['id'],
                    'name': char['name'],
                    'image_path': char['image_path'],
                    'ta': char['ta_score']
                }
                for char in characters
            ]
            
            return jsonify(success=True, tier_list=tier_list)
    except Exception as e:
        return jsonify(success=False, error=str(e))
