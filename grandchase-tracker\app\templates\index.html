{% extends "base.html" %}

{% block title %}GrandChase Tracker{% endblock %}

{% block content %}
    <!-- Overview Tab -->
    <div id="overview" class="tab-content">
        <div class="overview-summary">
            <div class="summary-stats">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                    <div class="stat-content">
                        <div class="stat-title">Total Characters</div>
                        <div class="stat-value" id="overview-total-chars">{{ characters|length }}</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="stat-content">
                        <div class="stat-title">Void Completions</div>
                        <div class="stat-value" id="overview-void-completions">0</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-dungeon"></i></div>
                    <div class="stat-content">
                        <div class="stat-title">Abyss Completions</div>
                        <div class="stat-value" id="overview-abyss-completions">0</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="stat-content">
                        <div class="stat-title">Average TA</div>
                        <div class="stat-value" id="overview-avg-ta">0</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="character-cards-grid">
            {% for character in characters %}
            <div class="character-card" data-character-id="{{ character.id }}">
                <div class="character-card-header">
                    <img src="{{ url_for('static', filename=character.image_path) }}" alt="{{ character.name }}" class="character-card-icon">
                    <div class="character-card-info">
                        <h3 class="character-card-name">{{ character.name }}</h3>
                        <div class="character-card-ta">
                            <span class="ta-label">TA:</span>
                            <input type="number" class="ta-card-input" id="overview-ta-{{ character.id }}" value="{{ character.ta_score }}" min="0">
                            <button class="ta-card-save" onclick="updateTAFromOverview({{ character.id }})"><i class="fas fa-check"></i></button>
                        </div>
                    </div>
                </div>

                <div class="character-card-content">
                    <div class="void-status-section">
                        <div class="void-status-row">
                            <div class="void-status-item void1" data-void="1">
                                <span class="void-label">Void 1:</span>
                                <span class="void-status" id="overview-void1-{{ character.id }}">Incomplete</span>
                            </div>
                            <div class="void-status-item void2" data-void="2">
                                <span class="void-label">Void 2:</span>
                                <span class="void-status" id="overview-void2-{{ character.id }}">Incomplete</span>
                            </div>
                            <div class="void-status-item void3" data-void="3">
                                <span class="void-label">Void 3:</span>
                                <span class="void-status" id="overview-void3-{{ character.id }}">Incomplete</span>
                            </div>
                        </div>
                    </div>

                    <div class="abyss-status-section">
                        <div class="abyss-status-item">
                            <span class="abyss-label">Abyss:</span>
                            <span class="abyss-status" id="overview-abyss-{{ character.id }}">Incomplete</span>
                        </div>
                    </div>

                    <div class="shards-summary">
                        <div class="shards-item">
                            <span class="shards-label">V1:</span>
                            <span class="shards-value" id="overview-shards1-{{ character.id }}">{{ character.void1_shards }}</span>
                        </div>
                        <div class="shards-item">
                            <span class="shards-label">V2:</span>
                            <span class="shards-value" id="overview-shards2-{{ character.id }}">{{ character.void2_shards }}</span>
                        </div>
                        <div class="shards-item">
                            <span class="shards-label">V3:</span>
                            <span class="shards-value" id="overview-shards3-{{ character.id }}">{{ character.void3_shards }}</span>
                        </div>
                    </div>
                </div>

                <div class="character-card-actions">
                    <div class="action-row">
                        <button class="action-btn complete-btn" onclick="toggleVoidFromOverview({{ character.id }}, 1)">
                            <i class="fas fa-check"></i> Complete V1
                        </button>
                        <button class="action-btn complete-btn" onclick="toggleVoidFromOverview({{ character.id }}, 2)">
                            <i class="fas fa-check"></i> Complete V2
                        </button>
                        <button class="action-btn complete-btn" onclick="toggleVoidFromOverview({{ character.id }}, 3)">
                            <i class="fas fa-check"></i> Complete V3
                        </button>
                    </div>
                    <div class="action-row">
                        <button class="action-btn reset-btn" onclick="resetVoidFromOverview({{ character.id }}, 1)">
                            <i class="fas fa-times"></i> Reset V1
                        </button>
                        <button class="action-btn reset-btn" onclick="resetVoidFromOverview({{ character.id }}, 2)">
                            <i class="fas fa-times"></i> Reset V2
                        </button>
                        <button class="action-btn reset-btn" onclick="resetVoidFromOverview({{ character.id }}, 3)">
                            <i class="fas fa-times"></i> Reset V3
                        </button>
                    </div>
                    <div class="action-row">
                        <button class="action-btn complete-btn" onclick="toggleAbyssFromOverview({{ character.id }})">
                            <i class="fas fa-check"></i> Complete Abyss
                        </button>
                        <button class="action-btn reset-btn" onclick="resetAbyssFromOverview({{ character.id }})">
                            <i class="fas fa-times"></i> Reset Abyss
                        </button>
                        <button class="action-btn edit-btn" onclick="openAccessoryModal({{ character.id }})">
                            <i class="fas fa-gem"></i> Edit Accessories
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Void Tracker -->
    <div id="void-tracker" class="tab-content">
        <div class="void-run-counts">
            <span id="void1-count" class="void-counter">Void 1: 0/23</span>
            <span id="void2-count" class="void-counter">Void 2: 0/23</span>
            <span id="void3-count" class="void-counter">Void 3: 0/23</span>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Character</th>
                    <th>Void 1</th>
                    <th>Void 2</th>
                    <th>Void 3</th>
                    <th>Void 1 Shards</th>
                    <th>Void 2 Shards</th>
                    <th>Void 3 Shards</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for character in characters %}
                <tr data-character-id="{{ character.id }}">
                    <td>
                        <img src="{{ url_for('static', filename=character.image_path) }}" alt="{{ character.name }}" class="character-icon">
                        {{ character.name }}
                    </td>
                    <td>
                        <div class="void-checkbox-container">
                            <input type="checkbox" id="void1-{{ character.id }}" {% if character.void1 %}checked{% endif %} onchange="updateVoid({{ character.id }})">
                            <select id="void1-level-{{ character.id }}" onchange="updateVoid({{ character.id }})">
                                <option value="1F" {% if character.void1_clear_level == 1 %}selected{% endif %}>1F</option>
                                <option value="2F" {% if character.void1_clear_level == 2 %}selected{% endif %}>2F</option>
                                <option value="3F" {% if character.void1_clear_level == 3 %}selected{% endif %}>3F</option>
                                <option value="4F" {% if character.void1_clear_level == 4 %}selected{% endif %}>4F</option>
                            </select>
                        </div>
                    </td>
                    <td>
                        <div class="void-checkbox-container">
                            <input type="checkbox" id="void2-{{ character.id }}" {% if character.void2 %}checked{% endif %} onchange="updateVoid({{ character.id }})">
                            <select id="void2-level-{{ character.id }}" onchange="updateVoid({{ character.id }})">
                                <option value="1F" {% if character.void2_clear_level == 1 %}selected{% endif %}>1F</option>
                                <option value="2F" {% if character.void2_clear_level == 2 %}selected{% endif %}>2F</option>
                                <option value="3F" {% if character.void2_clear_level == 3 %}selected{% endif %}>3F</option>
                                <option value="4F" {% if character.void2_clear_level == 4 %}selected{% endif %}>4F</option>
                            </select>
                        </div>
                    </td>
                    <td>
                        <div class="void-checkbox-container">
                            <input type="checkbox" id="void3-{{ character.id }}" {% if character.void3 %}checked{% endif %} onchange="updateVoid({{ character.id }})">
                            <select id="void3-level-{{ character.id }}" onchange="updateVoid({{ character.id }})">
                                <option value="1F" {% if character.void3_clear_level == 1 %}selected{% endif %}>1F</option>
                                <option value="2F" {% if character.void3_clear_level == 2 %}selected{% endif %}>2F</option>
                                <option value="3F" {% if character.void3_clear_level == 3 %}selected{% endif %}>3F</option>
                                <option value="4F" {% if character.void3_clear_level == 4 %}selected{% endif %}>4F</option>
                            </select>
                        </div>
                    </td>
                    <td class="void1-shards">
                        <div class="shard-display">
                            <span class="shard-value-display">{{ character.void1_shards }}</span>
                            <button class="edit-shard-btn" onclick="toggleShardEdit({{ character.id }}, 'void1')"><i class="fas fa-edit"></i></button>
                            <div class="shard-edit-container" style="display: none;">
                                <input type="number" class="shard-value" value="{{ character.void1_shards }}" min="0" onchange="updateShards({{ character.id }}, 'void1', this.value)">
                                <button class="save-shard-btn" onclick="toggleShardEdit({{ character.id }}, 'void1')"><i class="fas fa-check"></i></button>
                            </div>
                        </div>
                    </td>
                    <td class="void2-shards">
                        <div class="shard-display">
                            <span class="shard-value-display">{{ character.void2_shards }}</span>
                            <button class="edit-shard-btn" onclick="toggleShardEdit({{ character.id }}, 'void2')"><i class="fas fa-edit"></i></button>
                            <div class="shard-edit-container" style="display: none;">
                                <input type="number" class="shard-value" value="{{ character.void2_shards }}" min="0" onchange="updateShards({{ character.id }}, 'void2', this.value)">
                                <button class="save-shard-btn" onclick="toggleShardEdit({{ character.id }}, 'void2')"><i class="fas fa-check"></i></button>
                            </div>
                        </div>
                    </td>
                    <td class="void3-shards">
                        <div class="shard-display">
                            <span class="shard-value-display">{{ character.void3_shards }}</span>
                            <button class="edit-shard-btn" onclick="toggleShardEdit({{ character.id }}, 'void3')"><i class="fas fa-edit"></i></button>
                            <div class="shard-edit-container" style="display: none;">
                                <input type="number" class="shard-value" value="{{ character.void3_shards }}" min="0" onchange="updateShards({{ character.id }}, 'void3', this.value)">
                                <button class="save-shard-btn" onclick="toggleShardEdit({{ character.id }}, 'void3')"><i class="fas fa-check"></i></button>
                            </div>
                        </div>
                    </td>
                    <td class="action-container">
                        <div class="action-buttons">
                            <button class="action-toggle-btn" onclick="toggleActionButtons({{ character.id }})">Actions</button>
                            <div class="action-dropdown" id="action-dropdown-{{ character.id }}" style="display: none;">
                                <div class="convert-inputs">
                                    <input type="number" id="v1-convert-{{ character.id }}" placeholder="V1" min="0" max="{{ character.void1_shards }}" class="convert-input">
                                    <input type="number" id="v2-convert-{{ character.id }}" placeholder="V2" min="0" max="{{ character.void2_shards }}" class="convert-input">
                                    <button onclick="convertToV3({{ character.id }})" class="convert-button">Convert</button>
                                </div>
                                <div class="craft-buttons">
                                    <button onclick="craftVoidGear({{ character.id }}, 'void1')" class="craft-button">Craft V1</button>
                                    <button onclick="craftVoidGear({{ character.id }}, 'void2')" class="craft-button">Craft V2</button>
                                    <button onclick="craftVoidGear({{ character.id }}, 'void3')" class="craft-button">Craft V3</button>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- TA Tier List (Global) -->
    <div id="global-ta-tier-list" class="global-ta-tier-list" style="display: none;">
        <div id="ta-tier-list" class="ta-tier-list"></div>
        <div id="average-ta-section" class="average-ta-section">
            <h3 id="average-ta-display">Average TA: 0</h3>
        </div>
    </div>

    <!-- TA Tracker -->
    <div id="ta-tracker" class="tab-content">

        <table>
            <thead>
                <tr>
                    <th>Character</th>
                    <th>TA</th>
                </tr>
            </thead>
            <tbody>
                {% for character in characters %}
                <tr data-character-id="{{ character.id }}">
                    <td>
                        <img src="{{ url_for('static', filename=character.image_path) }}" alt="{{ character.name }}" class="character-icon">
                        {{ character.name }}
                    </td>
                    <td>
                        <div class="ta-input-container">
                            <input type="number" id="ta-input-{{ character.id }}" class="ta-input" value="{{ character.ta_score }}" min="0">
                            <button class="save-ta-btn" onclick="updateTA({{ character.id }})"><i class="fas fa-check"></i></button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="ta-settings-container">
            <h3 class="settings-header">Tier List Settings</h3>
            <div class="ta-settings">
                <div class="ta-tier-list-position">
                    <label>Default Position:</label>
                    <div class="radio-container">
                        <div class="radio-item">
                            <input type="radio" id="default-position-below-tabs" name="default-ta-tier-list-position" value="below-tabs" checked onchange="saveTASettings()">
                            <label for="default-position-below-tabs">Below Tabs</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="default-position-bottom" name="default-ta-tier-list-position" value="bottom" onchange="saveTASettings()">
                            <label for="default-position-bottom">Bottom</label>
                        </div>
                    </div>
                </div>

                <div class="ta-tier-list-tabs">
                    <label>Tab Settings:</label>
                    <div class="tab-settings-container">
                        <div class="tab-setting-item">
                            <div class="tab-setting-header">
                                <input type="checkbox" id="tab-void-tracker" value="void-tracker" onchange="saveTASettings()">
                                <label for="tab-void-tracker">Void Tracker</label>
                            </div>
                            <div class="tab-position-selector">
                                <div class="radio-item small">
                                    <input type="radio" id="position-void-tracker-below-tabs" name="position-void-tracker" value="below-tabs" checked onchange="saveTASettings()">
                                    <label for="position-void-tracker-below-tabs">Below Tabs</label>
                                </div>
                                <div class="radio-item small">
                                    <input type="radio" id="position-void-tracker-bottom" name="position-void-tracker" value="bottom" onchange="saveTASettings()">
                                    <label for="position-void-tracker-bottom">Bottom</label>
                                </div>
                            </div>
                        </div>

                        <div class="tab-setting-item">
                            <div class="tab-setting-header">
                                <input type="checkbox" id="tab-ta-tracker" value="ta-tracker" checked onchange="saveTASettings()">
                                <label for="tab-ta-tracker">TA Tracker</label>
                            </div>
                            <div class="tab-position-selector">
                                <div class="radio-item small">
                                    <input type="radio" id="position-ta-tracker-below-tabs" name="position-ta-tracker" value="below-tabs" checked onchange="saveTASettings()">
                                    <label for="position-ta-tracker-below-tabs">Below Tabs</label>
                                </div>
                                <div class="radio-item small">
                                    <input type="radio" id="position-ta-tracker-bottom" name="position-ta-tracker" value="bottom" onchange="saveTASettings()">
                                    <label for="position-ta-tracker-bottom">Bottom</label>
                                </div>
                            </div>
                        </div>

                        <div class="tab-setting-item">
                            <div class="tab-setting-header">
                                <input type="checkbox" id="tab-gear-tracker" value="gear-tracker" onchange="saveTASettings()">
                                <label for="tab-gear-tracker">Gear Tracker</label>
                            </div>
                            <div class="tab-position-selector">
                                <div class="radio-item small">
                                    <input type="radio" id="position-gear-tracker-below-tabs" name="position-gear-tracker" value="below-tabs" checked onchange="saveTASettings()">
                                    <label for="position-gear-tracker-below-tabs">Below Tabs</label>
                                </div>
                                <div class="radio-item small">
                                    <input type="radio" id="position-gear-tracker-bottom" name="position-gear-tracker" value="bottom" onchange="saveTASettings()">
                                    <label for="position-gear-tracker-bottom">Bottom</label>
                                </div>
                            </div>
                        </div>

                        <div class="tab-setting-item">
                            <div class="tab-setting-header">
                                <input type="checkbox" id="tab-abyss-tracker" value="abyss-tracker" onchange="saveTASettings()">
                                <label for="tab-abyss-tracker">Abyss Tracker</label>
                            </div>
                            <div class="tab-position-selector">
                                <div class="radio-item small">
                                    <input type="radio" id="position-abyss-tracker-below-tabs" name="position-abyss-tracker" value="below-tabs" checked onchange="saveTASettings()">
                                    <label for="position-abyss-tracker-below-tabs">Below Tabs</label>
                                </div>
                                <div class="radio-item small">
                                    <input type="radio" id="position-abyss-tracker-bottom" name="position-abyss-tracker" value="bottom" onchange="saveTASettings()">
                                    <label for="position-abyss-tracker-bottom">Bottom</label>
                                </div>
                            </div>
                        </div>

                        <div class="tab-setting-item">
                            <div class="tab-setting-header">
                                <input type="checkbox" id="tab-accessory-tracker" value="accessory-tracker" onchange="saveTASettings()">
                                <label for="tab-accessory-tracker">Accessory Tracker</label>
                            </div>
                            <div class="tab-position-selector">
                                <div class="radio-item small">
                                    <input type="radio" id="position-accessory-tracker-below-tabs" name="position-accessory-tracker" value="below-tabs" checked onchange="saveTASettings()">
                                    <label for="position-accessory-tracker-below-tabs">Below Tabs</label>
                                </div>
                                <div class="radio-item small">
                                    <input type="radio" id="position-accessory-tracker-bottom" name="position-accessory-tracker" value="bottom" onchange="saveTASettings()">
                                    <label for="position-accessory-tracker-bottom">Bottom</label>
                                </div>
                            </div>
                        </div>

                        <div class="tab-setting-item">
                            <div class="tab-setting-header">
                                <input type="checkbox" id="tab-tod-tracker" value="tod-tracker" onchange="saveTASettings()">
                                <label for="tab-tod-tracker">ToD Tracker</label>
                            </div>
                            <div class="tab-position-selector">
                                <div class="radio-item small">
                                    <input type="radio" id="position-tod-tracker-below-tabs" name="position-tod-tracker" value="below-tabs" checked onchange="saveTASettings()">
                                    <label for="position-tod-tracker-below-tabs">Below Tabs</label>
                                </div>
                                <div class="radio-item small">
                                    <input type="radio" id="position-tod-tracker-bottom" name="position-tod-tracker" value="bottom" onchange="saveTASettings()">
                                    <label for="position-tod-tracker-bottom">Bottom</label>
                                </div>
                            </div>
                        </div>

                        <div class="tab-setting-item">
                            <div class="tab-setting-header">
                                <input type="checkbox" id="tab-tod-history" value="tod-history" onchange="saveTASettings()">
                                <label for="tab-tod-history">ToD History</label>
                            </div>
                            <div class="tab-position-selector">
                                <div class="radio-item small">
                                    <input type="radio" id="position-tod-history-below-tabs" name="position-tod-history" value="below-tabs" checked onchange="saveTASettings()">
                                    <label for="position-tod-history-below-tabs">Below Tabs</label>
                                </div>
                                <div class="radio-item small">
                                    <input type="radio" id="position-tod-history-bottom" name="position-tod-history" value="bottom" onchange="saveTASettings()">
                                    <label for="position-tod-history-bottom">Bottom</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gear Tracker -->
    <div id="gear-tracker" class="tab-content">
        <div class="void-run-counts gear-counts">
            <span id="void1-gear-count" class="void-counter">Void 1 Gear: 0/23</span>
            <span id="void2-gear-count" class="void-counter">Void 2 Gear: 0/69</span>
            <span id="void3-gear-count" class="void-counter">Void 3 Gear: 0/69</span>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Character</th>
                    <th><img src="{{ url_for('static', filename='images/gear/weapon_icon.png') }}" alt="Weapon" class="gear-icon"> Weapon</th>
                    <th><img src="{{ url_for('static', filename='images/gear/helmet_icon.png') }}" alt="Helmet" class="gear-icon"> Helmet</th>
                    <th><img src="{{ url_for('static', filename='images/gear/armor_icon.png') }}" alt="Armor" class="gear-icon"> Armor</th>
                    <th><img src="{{ url_for('static', filename='images/gear/pants_icon.png') }}" alt="Pants" class="gear-icon"> Pants</th>
                    <th><img src="{{ url_for('static', filename='images/gear/gloves_icon.png') }}" alt="Gloves" class="gear-icon"> Gloves</th>
                    <th><img src="{{ url_for('static', filename='images/gear/shoes_icon.png') }}" alt="Shoes" class="gear-icon"> Shoes</th>
                    <th><img src="{{ url_for('static', filename='images/gear/cloak_icon.png') }}" alt="Cloak" class="gear-icon"> Cloak</th>
                </tr>
            </thead>
            <tbody>
                {% for character in characters %}
                <tr data-character-id="{{ character.id }}">
                    <td>
                        <img src="{{ url_for('static', filename=character.image_path) }}" alt="{{ character.name }}" class="character-icon">
                        {{ character.name }}
                    </td>
                    <td>
                        <input type="checkbox" {% if character.weapon %}checked{% endif %} onchange="updateGear({{ character.id }}, 'weapon', this.checked)">
                    </td>
                    <td>
                        <input type="checkbox" {% if character.helmet %}checked{% endif %} onchange="updateGear({{ character.id }}, 'helmet', this.checked)">
                    </td>
                    <td>
                        <input type="checkbox" {% if character.armor %}checked{% endif %} onchange="updateGear({{ character.id }}, 'armor', this.checked)">
                    </td>
                    <td>
                        <input type="checkbox" {% if character.pants %}checked{% endif %} onchange="updateGear({{ character.id }}, 'pants', this.checked)">
                    </td>
                    <td>
                        <input type="checkbox" {% if character.gloves %}checked{% endif %} onchange="updateGear({{ character.id }}, 'gloves', this.checked)">
                    </td>
                    <td>
                        <input type="checkbox" {% if character.shoes %}checked{% endif %} onchange="updateGear({{ character.id }}, 'shoes', this.checked)">
                    </td>
                    <td>
                        <input type="checkbox" {% if character.cloak %}checked{% endif %} onchange="updateGear({{ character.id }}, 'cloak', this.checked)">
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Abyss Tracker -->
    <div id="abyss-tracker" class="tab-content">
        <div class="void-run-counts abyss-run-counts">
            <span id="abyss-completion" class="void-counter">Abyss: 0/23</span>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Character</th>
                    <th>Completed</th>
                </tr>
            </thead>
            <tbody>
                {% for character in characters %}
                <tr data-character-id="{{ character.id }}">
                    <td>
                        <img src="{{ url_for('static', filename=character.image_path) }}" alt="{{ character.name }}" class="character-icon">
                        {{ character.name }}
                    </td>
                    <td>
                        <input type="checkbox" {% if character.abyss_completed %}checked{% endif %} onchange="updateAbyss({{ character.id }}, this.checked)">
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Accessory Tracker -->
    <div id="accessory-tracker" class="tab-content">
        <table>
            <thead>
                <tr>
                    <th>Character</th>
                    <th>Ring</th>
                    <th>Earring</th>
                    <th>Piercing</th>
                </tr>
            </thead>
            <tbody>
                {% for character in characters %}
                <tr data-character-id="{{ character.id }}">
                    <td>
                        <img src="{{ url_for('static', filename=character.image_path) }}" alt="{{ character.name }}" class="character-icon">
                        {{ character.name }}
                    </td>
                    <td>
                        <div class="accessory-selector">
                            <select onchange="updateAccessory({{ character.id }}, 'ring', this.value)">
                                <option value="Unkeepable Promise III" {% if character.ring == 'Unkeepable Promise III' %}selected{% endif %}>Unkeepable Promise III</option>
                                <option value="Shining Ring of Infinity III" {% if character.ring == 'Shining Ring of Infinity III' %}selected{% endif %}>Shining Ring of Infinity III</option>
                                <option value="Forged Ring of Infinity III" {% if character.ring == 'Forged Ring of Infinity III' %}selected{% endif %}>Forged Ring of Infinity III</option>
                                <option value="Faded Ring of Infinity III" {% if character.ring == 'Faded Ring of Infinity III' %}selected{% endif %}>Faded Ring of Infinity III</option>
                                <option value="Shining Ring of Dimension III" {% if character.ring == 'Shining Ring of Dimension III' %}selected{% endif %}>Shining Ring of Dimension III</option>
                                <option value="Forged Ring of Dimension III" {% if character.ring == 'Forged Ring of Dimension III' %}selected{% endif %}>Forged Ring of Dimension III</option>
                                <option value="Faded Ring of Dimension III" {% if character.ring == 'Faded Ring of Dimension III' %}selected{% endif %}>Faded Ring of Dimension III</option>
                                <option value="Unkeepable Promise II" {% if character.ring == 'Unkeepable Promise II' %}selected{% endif %}>Unkeepable Promise II</option>
                                <option value="Shining Ring of Infinity II" {% if character.ring == 'Shining Ring of Infinity II' %}selected{% endif %}>Shining Ring of Infinity II</option>
                                <option value="Forged Ring of Infinity II" {% if character.ring == 'Forged Ring of Infinity II' %}selected{% endif %}>Forged Ring of Infinity II</option>
                                <option value="Faded Ring of Infinity II" {% if character.ring == 'Faded Ring of Infinity II' %}selected{% endif %}>Faded Ring of Infinity II</option>
                                <option value="Shining Ring of Dimension II" {% if character.ring == 'Shining Ring of Dimension II' %}selected{% endif %}>Shining Ring of Dimension II</option>
                                <option value="Forged Ring of Dimension II" {% if character.ring == 'Forged Ring of Dimension II' %}selected{% endif %}>Forged Ring of Dimension II</option>
                                <option value="Faded Ring of Dimension II" {% if character.ring == 'Faded Ring of Dimension II' %}selected{% endif %}>Faded Ring of Dimension II</option>
                                <option value="Unkeepable Promise I" {% if character.ring == 'Unkeepable Promise I' %}selected{% endif %}>Unkeepable Promise I</option>
                                <option value="Shining Ring of Infinity I" {% if character.ring == 'Shining Ring of Infinity I' %}selected{% endif %}>Shining Ring of Infinity I</option>
                                <option value="Forged Ring of Infinity I" {% if character.ring == 'Forged Ring of Infinity I' %}selected{% endif %}>Forged Ring of Infinity I</option>
                                <option value="Faded Ring of Infinity I" {% if character.ring == 'Faded Ring of Infinity I' %}selected{% endif %}>Faded Ring of Infinity I</option>
                                <option value="Shining Ring of Dimension I" {% if character.ring == 'Shining Ring of Dimension I' %}selected{% endif %}>Shining Ring of Dimension I</option>
                                <option value="Forged Ring of Dimension I" {% if character.ring == 'Forged Ring of Dimension I' %}selected{% endif %}>Forged Ring of Dimension I</option>
                                <option value="Faded Ring of Dimension I" {% if character.ring == 'Faded Ring of Dimension I' %}selected{% endif %}>Faded Ring of Dimension I</option>
                                <option value="Harkyon" {% if character.ring == 'Harkyon' %}selected{% endif %}>Harkyon</option>
                            </select>
                            <img id="ring-icon-{{ character.id }}" src="{{ url_for('static', filename='images/accessories/rings/' + (character.ring | replace(' ', '_') + '.png')) }}" alt="{{ character.ring }}" class="accessory-icon">
                        </div>
                    </td>
                    <td>
                        <div class="accessory-selector">
                            <select onchange="updateAccessory({{ character.id }}, 'earring', this.value)">
                                <option value="Chaos" {% if character.earring == 'Chaos' %}selected{% endif %}>Chaos</option>
                                <option value="Relic Dimensional" {% if character.earring == 'Relic Dimensional' %}selected{% endif %}>Relic Dimensional</option>
                                <option value="Epic Dimensional" {% if character.earring == 'Epic Dimensional' %}selected{% endif %}>Epic Dimensional</option>
                                <option value="Rare Dimensional" {% if character.earring == 'Rare Dimensional' %}selected{% endif %}>Rare Dimensional</option>
                                <option value="Order" {% if character.earring == 'Order' %}selected{% endif %}>Order</option>
                            </select>
                            <img id="earring-icon-{{ character.id }}" src="{{ url_for('static', filename='images/accessories/earrings/' + (character.earring | replace(' ', '_') + '.png')) }}" alt="{{ character.earring }}" class="accessory-icon">
                        </div>
                    </td>
                    <td>
                        <div class="accessory-selector">
                            <select onchange="updateAccessory({{ character.id }}, 'piercing', this.value)">
                                <option value="Chaos" {% if character.piercing == 'Chaos' %}selected{% endif %}>Chaos</option>
                                <option value="Relic Dimensional" {% if character.piercing == 'Relic Dimensional' %}selected{% endif %}>Relic Dimensional</option>
                                <option value="Epic Dimensional" {% if character.piercing == 'Epic Dimensional' %}selected{% endif %}>Epic Dimensional</option>
                                <option value="Rare Dimensional" {% if character.piercing == 'Rare Dimensional' %}selected{% endif %}>Rare Dimensional</option>
                                <option value="Order" {% if character.piercing == 'Order' %}selected{% endif %}>Order</option>
                            </select>
                            <img id="piercing-icon-{{ character.id }}" src="{{ url_for('static', filename='images/accessories/piercings/' + (character.piercing | replace(' ', '_') + '.png')) }}" alt="{{ character.piercing }}" class="accessory-icon">
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- ToD Tracker -->
    <div id="tod-tracker" class="tab-content">
        <div class="void-run-counts tod-run-counts">
            <span id="tod-completion" class="void-counter">ToD: 0/23</span>
            <span id="tod-current-date" class="tod-date"></span>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Character</th>
                    <th>Floor</th>
                    <th colspan="2">Run 1</th>
                    <th colspan="2">Run 2</th>
                    <th colspan="2">Run 3</th>
                    <th class="cleared-header">Cleared</th>
                </tr>
                <tr>
                    <th></th>
                    <th></th>
                    <th class="drop-floor" style="color: #4e54c8;">Run 1<br>5F</th>
                    <th class="drop-floor" style="color: #f7b733;">Run 1<br>10F</th>
                    <th class="drop-floor" style="color: #4e54c8;">Run 2<br>5F</th>
                    <th class="drop-floor" style="color: #f7b733;">Run 2<br>10F</th>
                    <th class="drop-floor" style="color: #4e54c8;">Run 3<br>5F</th>
                    <th class="drop-floor" style="color: #f7b733;">Run 3<br>10F</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                {% for character in characters %}
                <tr data-character-id="{{ character.id }}">
                    <td>
                        <img src="{{ url_for('static', filename=character.image_path) }}" alt="{{ character.name }}" class="character-icon">
                        {{ character.name }}
                    </td>
                    <td>
                        <select id="floor-select-{{ character.id }}" onchange="updateToDFloorSelection({{ character.id }})">
                            <option value="5" {% if character.tod_floor == 5 %}selected{% endif %}>5F</option>
                            <option value="10" {% if character.tod_floor == 10 %}selected{% endif %}>10F</option>
                        </select>
                    </td>
                    <td class="drop-cell">
                        <div class="drop-toggle" id="toggle-run1-5f-{{ character.id }}">
                            <input type="checkbox" id="run1-5f-{{ character.id }}" onchange="saveDropData({{ character.id }})">
                            <label for="run1-5f-{{ character.id }}">SPR</label>
                        </div>
                    </td>
                    <td class="drop-cell">
                        <div class="drop-toggle" id="toggle-run1-10f-{{ character.id }}">
                            <input type="checkbox" id="run1-10f-{{ character.id }}" onchange="saveDropData({{ character.id }})">
                            <label for="run1-10f-{{ character.id }}">SPR</label>
                        </div>
                    </td>
                    <td class="drop-cell">
                        <div class="drop-toggle" id="toggle-run2-5f-{{ character.id }}">
                            <input type="checkbox" id="run2-5f-{{ character.id }}" onchange="saveDropData({{ character.id }})">
                            <label for="run2-5f-{{ character.id }}">SPR</label>
                        </div>
                    </td>
                    <td class="drop-cell">
                        <div class="drop-toggle" id="toggle-run2-10f-{{ character.id }}">
                            <input type="checkbox" id="run2-10f-{{ character.id }}" onchange="saveDropData({{ character.id }})">
                            <label for="run2-10f-{{ character.id }}">SPR</label>
                        </div>
                    </td>
                    <td class="drop-cell">
                        <div class="drop-toggle" id="toggle-run3-5f-{{ character.id }}">
                            <input type="checkbox" id="run3-5f-{{ character.id }}" onchange="saveDropData({{ character.id }})">
                            <label for="run3-5f-{{ character.id }}">SPR</label>
                        </div>
                    </td>
                    <td class="drop-cell">
                        <div class="drop-toggle" id="toggle-run3-10f-{{ character.id }}">
                            <input type="checkbox" id="run3-10f-{{ character.id }}" onchange="saveDropData({{ character.id }})">
                            <label for="run3-10f-{{ character.id }}">SPR</label>
                        </div>
                    </td>
                    <td class="cleared-cell">
                        <div class="cleared-toggle">
                            <input type="checkbox"
                                   id="tod-cleared-{{ character.id }}"
                                   {% if character.tod_cleared %}checked{% endif %}
                                   onchange="updateToD({{ character.id }}, this.checked)">
                            <label for="tod-cleared-{{ character.id }}">
                                <i class="fas fa-check"></i>
                                <span class="cleared-text">Cleared</span>
                            </label>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="preference-container">
            <h3 class="settings-header">Default Settings</h3>
            <div class="tod-settings">
                <div class="floor-preference">
                    <label for="default-floor-preference">Default Floor:</label>
                    <select id="default-floor-preference" onchange="saveDefaultFloorPreference()">
                        <option value="5">5F</option>
                        <option value="10" selected>10F</option>
                    </select>
                    <button class="apply-to-all" onclick="applyDefaultFloorToAll()">Apply to All</button>
                </div>
            </div>
        </div>
    </div>

    <!-- ToD History -->
    <div id="tod-history" class="tab-content">
        <div class="date-range-selector">
            <label for="start-date">Start Date:</label>
            <input type="date" id="start-date">
            <label for="end-date">End Date:</label>
            <input type="date" id="end-date">
            <button onclick="loadToDHistory()">Load History</button>
        </div>

        <!-- Summary Cards -->
        <div class="tod-summary-cards">
            <div class="summary-card runs-card">
                <div class="card-icon"><i class="fas fa-running"></i></div>
                <div class="card-content">
                    <div class="card-title">Total Runs</div>
                    <div class="card-value" id="total-runs">0</div>
                </div>
            </div>
            <div class="summary-card drops-card">
                <div class="card-icon"><i class="fas fa-gem"></i></div>
                <div class="card-content">
                    <div class="card-title">Total Drops</div>
                    <div class="card-value" id="total-drops">0</div>
                </div>
            </div>
            <div class="summary-card rate-card">
                <div class="card-icon"><i class="fas fa-percentage"></i></div>
                <div class="card-content">
                    <div class="card-title">Average Drop Rate</div>
                    <div class="card-value" id="avg-drop-rate">0.00%</div>
                </div>
            </div>
            <div class="summary-card best-floor-card">
                <div class="card-icon"><i class="fas fa-trophy"></i></div>
                <div class="card-content">
                    <div class="card-title">Best Drop Floor</div>
                    <div class="card-value" id="best-drop-floor">-</div>
                </div>
            </div>
        </div>

        <!-- Detailed Stats -->
        <div class="tod-stats-container">
            <!-- Character Stats Section -->
            <div class="stats-section">
                <div class="section-header run-stats-header">Char Stats</div>
                <div class="stats-row">
                    <div class="stats-label"><i class="fas fa-check-circle"></i> 5F Char Clears</div>
                    <div class="stats-value" id="5f-clears">0</div>
                </div>
                <div class="stats-row">
                    <div class="stats-label"><i class="fas fa-check-circle"></i> 10F Char Clears</div>
                    <div class="stats-value" id="10f-clears">0</div>
                </div>
            </div>

            <!-- Floor-Specific Drop Analysis -->
            <div class="stats-section">
                <div class="section-header drop-analysis-header">Floor-Specific Drop Analysis</div>
                <div class="stats-row">
                    <div class="stats-label"><i class="fas fa-gem"></i> 5F Floor Drops (from 5F runs)</div>
                    <div class="stats-value" id="5f-drops-from-5f">0</div>
                </div>
                <div class="stats-row">
                    <div class="stats-label"><i class="fas fa-gem"></i> 5F Floor Drops (from 10F runs)</div>
                    <div class="stats-value" id="5f-drops-from-10f">0</div>
                </div>
                <div class="stats-row">
                    <div class="stats-label"><i class="fas fa-gem"></i> 10F Floor Drops</div>
                    <div class="stats-value" id="10f-drops">0</div>
                </div>
            </div>

            <!-- Drop Rates -->
            <div class="stats-section">
                <div class="section-header drop-rates-header">Drop Rates</div>
                <div class="stats-row">
                    <div class="stats-label"><i class="fas fa-percentage"></i> 5F Floor Drop Rate (5F runs)</div>
                    <div class="stats-value" id="5f-drop-rate-5f">0.00%</div>
                </div>
                <div class="stats-row">
                    <div class="stats-label"><i class="fas fa-percentage"></i> 5F Floor Drop Rate (10F runs)</div>
                    <div class="stats-value" id="5f-drop-rate-10f">0.00%</div>
                </div>
                <div class="stats-row">
                    <div class="stats-label"><i class="fas fa-percentage"></i> 10F Floor Drop Rate</div>
                    <div class="stats-value" id="10f-drop-rate">0.00%</div>
                </div>
            </div>
        </div>

        <!-- Drop Rate Comparison -->
        <div class="drop-rate-comparison">
            <div class="comparison-header">Drop Rate Comparison</div>
            <div class="comparison-bars">
                <div class="comparison-row">
                    <div class="comparison-label">5F (from 5F runs)</div>
                    <div class="comparison-bar-container">
                        <div class="comparison-bar" id="5f-5f-bar" style="width: 0%;">0.00%</div>
                    </div>
                </div>
                <div class="comparison-row">
                    <div class="comparison-label">5F (from 10F runs)</div>
                    <div class="comparison-bar-container">
                        <div class="comparison-bar" id="5f-10f-bar" style="width: 0%;">0.00%</div>
                    </div>
                </div>
                <div class="comparison-row">
                    <div class="comparison-label">10F</div>
                    <div class="comparison-bar-container">
                        <div class="comparison-bar" id="10f-bar" style="width: 0%;">0.00%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
<script>
    // Initialize with today's date when history tab is opened
    document.addEventListener('DOMContentLoaded', function() {
        const todHistoryTab = document.getElementById('tod-history');
        const tabButton = document.querySelector('.tab-button[onclick="showTab(\'tod-history\')"]');

        if (todHistoryTab && tabButton) {
            tabButton.addEventListener('click', function() {
                const today = new Date();
                const oneMonthAgo = new Date();
                oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

                document.getElementById('start-date').value = formatDate(oneMonthAgo);
                document.getElementById('end-date').value = formatDate(today);

                // Load history data
                loadToDHistory();
            });
        }
    });

    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
</script>
{% endblock %}
