/**
 * GrandChase Tracker - Void JavaScript
 * Contains functionality for the Void Tracker tab
 */

// Update void status for a character
function updateVoid(characterId) {
    const void1Checkbox = document.getElementById(`void1-${characterId}`);
    const void2Checkbox = document.getElementById(`void2-${characterId}`);
    const void3Checkbox = document.getElementById(`void3-${characterId}`);

    const void1ClearLevel = document.getElementById(`void1-level-${characterId}`).value;
    const void2ClearLevel = document.getElementById(`void2-level-${characterId}`).value;
    const void3ClearLevel = document.getElementById(`void3-level-${characterId}`).value;

    // Convert clear level to integer (1F = 1, 2F = 2, etc.)
    const void1Level = void1ClearLevel ? parseInt(void1ClearLevel.charAt(0)) : 3;
    const void2Level = void2ClearLevel ? parseInt(void2ClearLevel.charAt(0)) : 3;
    const void3Level = void3ClearLevel ? parseInt(void3ClearLevel.charAt(0)) : 3;

    axios.post('/update', {
        character_id: characterId,
        void1: void1Checkbox.checked,
        void2: void2Checkbox.checked,
        void3: void3Checkbox.checked,
        void1_clear_level: void1Level,
        void2_clear_level: void2Level,
        void3_clear_level: void3Level
    })
    .then(function (response) {
        if (response.data.success) {
            console.log('Void status updated successfully');
            updateVoidRunCounts();

            // Update shard values if they were returned
            if (response.data.void1_shards !== undefined) {
                updateShardDisplays(characterId, {
                    void1_shards: response.data.void1_shards,
                    void2_shards: response.data.void2_shards,
                    void3_shards: response.data.void3_shards
                });
            }

            showToast(`Updated void status for character #${characterId}`);
        } else {
            console.error('Failed to update void status:', response.data.error);
            showToast('Error updating void status', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error updating void status:', error);
        showToast('Error updating void status', 'error');
    });
}

// Load void status data for a character
function loadVoidStatusData(characterId) {
    axios.get(`/get_void_data?character_id=${characterId}`)
    .then(function (response) {
        if (response.data.success) {
            // Update checkboxes
            const void1Checkbox = document.getElementById(`void1-${characterId}`);
            const void2Checkbox = document.getElementById(`void2-${characterId}`);
            const void3Checkbox = document.getElementById(`void3-${characterId}`);

            if (void1Checkbox) void1Checkbox.checked = response.data.void1;
            if (void2Checkbox) void2Checkbox.checked = response.data.void2;
            if (void3Checkbox) void3Checkbox.checked = response.data.void3;

            // Update clear levels
            const void1LevelSelect = document.getElementById(`void1-level-${characterId}`);
            const void2LevelSelect = document.getElementById(`void2-level-${characterId}`);
            const void3LevelSelect = document.getElementById(`void3-level-${characterId}`);

            if (void1LevelSelect) void1LevelSelect.value = `${response.data.void1_clear_level}F`;
            if (void2LevelSelect) void2LevelSelect.value = `${response.data.void2_clear_level}F`;
            if (void3LevelSelect) void3LevelSelect.value = `${response.data.void3_clear_level}F`;
        }
    })
    .catch(function (error) {
        console.error(`Error loading void data for character ${characterId}:`, error);
    });
}

// Update void run counts
function updateVoidRunCounts() {
    axios.get('/get_void_run_counts')
    .then(function (response) {
        if (response.data.success) {
            const void1Count = response.data.void1_count;
            const void2Count = response.data.void2_count;
            const void3Count = response.data.void3_count;
            const totalCharacters = response.data.total_characters;

            // Update counters with progress bars
            updateCounterWithProgressBar('void1-count', void1Count, totalCharacters);
            updateCounterWithProgressBar('void2-count', void2Count, totalCharacters);
            updateCounterWithProgressBar('void3-count', void3Count, totalCharacters);
        } else {
            console.error('Failed to get void run counts:', response.data.error);
            showToast('Error getting void run counts', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error getting void run counts:', error);
        showToast('Error getting void run counts', 'error');
    });
}

// Reset void1 for all characters
function resetVoid1() {
    Modal.confirm('Are you sure you want to reset Void 1? This will clear all Void 1 completions.', function() {
        axios.post('/reset_void1')
        .then(function (response) {
            if (response.data.success) {
                console.log('Void 1 reset successfully');

                // Uncheck all void1 checkboxes
                document.querySelectorAll('input[id^="void1-"]').forEach(checkbox => {
                    if (checkbox.type === 'checkbox') {
                        checkbox.checked = false;
                    }
                });

                updateVoidRunCounts();
                showToast('All Void 1 completions have been reset');
            } else {
                console.error('Failed to reset Void 1:', response.data.error);
                showToast('Error resetting Void 1', 'error');
            }
        })
        .catch(function (error) {
            console.error('Error resetting Void 1:', error);
            showToast('Error resetting Void 1', 'error');
        });
    });
}

// Reset void2 for all characters
function resetVoid2() {
    Modal.confirm('Are you sure you want to reset Void 2? This will clear all Void 2 completions.', function() {
        axios.post('/reset_void2')
        .then(function (response) {
            if (response.data.success) {
                console.log('Void 2 reset successfully');

                // Uncheck all void2 checkboxes
                document.querySelectorAll('input[id^="void2-"]').forEach(checkbox => {
                    if (checkbox.type === 'checkbox') {
                        checkbox.checked = false;
                    }
                });

                updateVoidRunCounts();
                showToast('All Void 2 completions have been reset');
            } else {
                console.error('Failed to reset Void 2:', response.data.error);
                showToast('Error resetting Void 2', 'error');
            }
        })
        .catch(function (error) {
            console.error('Error resetting Void 2:', error);
            showToast('Error resetting Void 2', 'error');
        });
    });
}

// Reset void3 for all characters
function resetVoid3() {
    Modal.confirm('Are you sure you want to reset Void 3? This will clear all Void 3 completions.', function() {
        axios.post('/reset_void3')
        .then(function (response) {
            if (response.data.success) {
                console.log('Void 3 reset successfully');

                // Uncheck all void3 checkboxes
                document.querySelectorAll('input[id^="void3-"]').forEach(checkbox => {
                    if (checkbox.type === 'checkbox') {
                        checkbox.checked = false;
                    }
                });

                updateVoidRunCounts();
                showToast('All Void 3 completions have been reset');
            } else {
                console.error('Failed to reset Void 3:', response.data.error);
                showToast('Error resetting Void 3', 'error');
            }
        })
        .catch(function (error) {
            console.error('Error resetting Void 3:', error);
            showToast('Error resetting Void 3', 'error');
        });
    });
}

// Update shard count for a character
function updateShards(characterId, shardType, shardValue) {
    axios.post('/update_shards', {
        character_id: characterId,
        shard_type: shardType,
        shard_value: parseInt(shardValue)
    })
    .then(function (response) {
        if (response.data.success) {
            console.log('Shards updated successfully');

            // Update the shard display
            const shards = {};
            shards[`${shardType}_shards`] = response.data.shard_value;
            updateShardDisplays(characterId, shards);

            // Hide the edit container
            const shardEditContainer = document.querySelector(`tr[data-character-id="${characterId}"] .${shardType}-shards .shard-edit-container`);
            if (shardEditContainer) {
                shardEditContainer.style.display = 'none';
            }

            showToast(`Updated ${shardType} shards for character #${characterId}`);
        } else {
            console.error('Failed to update shards:', response.data.error);
            showToast('Error updating shards', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error updating shards:', error);
        showToast('Error updating shards', 'error');
    });
}

// Toggle shard edit mode
function toggleShardEdit(characterId, shardType) {
    const shardDisplay = document.querySelector(`tr[data-character-id="${characterId}"] .${shardType}-shards .shard-display`);
    const shardValueDisplay = shardDisplay.querySelector('.shard-value-display');
    const shardEditContainer = shardDisplay.querySelector('.shard-edit-container');

    if (shardEditContainer.style.display === 'none') {
        // Show edit container
        shardEditContainer.style.display = 'flex';

        // Focus on the input
        const input = shardEditContainer.querySelector('.shard-value');
        input.focus();
        input.select();
    } else {
        // Hide edit container
        shardEditContainer.style.display = 'none';

        // Update the display value
        const input = shardEditContainer.querySelector('.shard-value');
        shardValueDisplay.textContent = input.value;
    }
}

// Toggle action buttons
function toggleActionButtons(characterId) {
    const actionDropdown = document.getElementById(`action-dropdown-${characterId}`);

    // Close all other open dropdowns
    document.querySelectorAll('.action-dropdown').forEach(dropdown => {
        if (dropdown.id !== `action-dropdown-${characterId}` && dropdown.style.display !== 'none') {
            dropdown.style.display = 'none';
        }
    });

    // Toggle this dropdown
    if (actionDropdown.style.display === 'none') {
        actionDropdown.style.display = 'block';
    } else {
        actionDropdown.style.display = 'none';
    }
}

// Convert void1 and void2 shards to void3 shards
function convertToV3(characterId) {
    const v1Convert = parseInt(document.getElementById(`v1-convert-${characterId}`).value) || 0;
    const v2Convert = parseInt(document.getElementById(`v2-convert-${characterId}`).value) || 0;

    if (v1Convert < 0 || v2Convert < 0) {
        showToast('Please enter valid numbers for conversion', 'error');
        return;
    }

    // Calculate expected V3 shards
    const v1ToV3 = Math.floor(v1Convert / 4);
    const v2ToV3 = Math.floor(v2Convert / 4);
    const totalV3 = v1ToV3 + v2ToV3;

    if (v1Convert === 0 && v2Convert === 0) {
        showToast('Please enter at least one value for conversion', 'error');
        return;
    }

    // Use Modal.confirm for a better looking confirmation dialog
    Modal.confirm(
        `<div class="conversion-confirm">
            <h3>Confirm Shard Conversion</h3>
            <div class="conversion-details">
                <div class="conversion-row">
                    <span>${v1Convert} V1 shards</span>
                    <span class="arrow">→</span>
                    <span>${v1ToV3} V3 shards</span>
                </div>
                <div class="conversion-row">
                    <span>${v2Convert} V2 shards</span>
                    <span class="arrow">→</span>
                    <span>${v2ToV3} V3 shards</span>
                </div>
                <div class="conversion-total">
                    <span>Total gain:</span>
                    <span>${totalV3} V3 shards</span>
                </div>
            </div>
            <div class="conversion-ratio">
                <p>Conversion ratio:</p>
                <ul>
                    <li>4 V1 shards = 1 V3 shard</li>
                    <li>4 V2 shards = 1 V3 shard</li>
                </ul>
            </div>
        </div>`,
        function() {
            // Continue with conversion
            performConversion();
        }
    );

    return; // Stop here and wait for confirmation

    // This function will be called if the user confirms
    function performConversion() {
        axios.post('/convert_to_v3', {
            character_id: characterId,
            v1_convert: v1Convert,
            v2_convert: v2Convert
        })
        .then(function (response) {
            if (response.data.success) {
                console.log('Shards converted successfully');

                // Update shard displays with new values
                updateShardDisplays(characterId, {
                    void1_shards: response.data.void1_shards,
                    void2_shards: response.data.void2_shards,
                    void3_shards: response.data.void3_shards
                });

                // Clear conversion inputs
                document.getElementById(`v1-convert-${characterId}`).value = '';
                document.getElementById(`v2-convert-${characterId}`).value = '';

                // Hide the action dropdown
                document.getElementById(`action-dropdown-${characterId}`).style.display = 'none';

                showToast(`Converted shards for character #${characterId}`);
            } else {
                console.error('Failed to convert shards:', response.data.error);
                showToast('Error converting shards: ' + response.data.error, 'error');
            }
        })
        .catch(function (error) {
            console.error('Error converting shards:', error);
            showToast('Error converting shards', 'error');
        });
    }
}

// Update shard displays
function updateShardDisplays(characterId, shards) {
    if (shards.void1_shards !== undefined) {
        const void1Display = document.querySelector(`tr[data-character-id="${characterId}"] .void1-shards .shard-value-display`);
        const void1Input = document.querySelector(`tr[data-character-id="${characterId}"] .void1-shards .shard-value`);

        if (void1Display) void1Display.textContent = shards.void1_shards;
        if (void1Input) void1Input.value = shards.void1_shards;
    }

    if (shards.void2_shards !== undefined) {
        const void2Display = document.querySelector(`tr[data-character-id="${characterId}"] .void2-shards .shard-value-display`);
        const void2Input = document.querySelector(`tr[data-character-id="${characterId}"] .void2-shards .shard-value`);

        if (void2Display) void2Display.textContent = shards.void2_shards;
        if (void2Input) void2Input.value = shards.void2_shards;
    }

    if (shards.void3_shards !== undefined) {
        const void3Display = document.querySelector(`tr[data-character-id="${characterId}"] .void3-shards .shard-value-display`);
        const void3Input = document.querySelector(`tr[data-character-id="${characterId}"] .void3-shards .shard-value`);

        if (void3Display) void3Display.textContent = shards.void3_shards;
        if (void3Input) void3Input.value = shards.void3_shards;
    }
}

// Initialize Void Tracker when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Void Tracker');

    // Load void status data for all characters
    document.querySelectorAll('tr[data-character-id]').forEach(row => {
        const characterId = row.dataset.characterId;
        // Only load data if we're in the void tracker tab (check for void checkboxes)
        if (document.getElementById(`void1-${characterId}`)) {
            loadVoidStatusData(characterId);
        }
    });
});

// Craft gear for a character (Void Tracker version - doesn't update gear database)
function craftVoidGear(characterId, gearType) {
    const fragmentReduction = 60; // Fragments reduced per craft

    // Custom confirmation modal
    Modal.confirm(
        `Are you sure you want to craft ${gearType.toUpperCase()} gear? This will reduce ${gearType.toUpperCase()} fragments by ${fragmentReduction}.`,
        function() {
            axios.post('/craft_void_gear', {
                character_id: characterId,
                gear_type: gearType,
                reduction: fragmentReduction
            })
            .then(function (response) {
                if (response.data.success) {
                    console.log(`${gearType} gear crafted successfully for character ${characterId}`);

                    // Update shard displays
                    updateShardDisplays(characterId, {
                        void1_shards: response.data.void1_shards,
                        void2_shards: response.data.void2_shards,
                        void3_shards: response.data.void3_shards
                    });

                    // Hide the action dropdown
                    document.getElementById(`action-dropdown-${characterId}`).style.display = 'none';

                    // Note: We don't update gear checkboxes when crafting from the Void Tracker
                    // This is just for shard tracking purposes

                    showToast(`Successfully crafted ${gearType} gear for character #${characterId}`);
                } else {
                    console.error('Failed to craft gear:', response.data.error);
                    showToast('Error crafting gear: ' + response.data.error, 'error');
                }
            })
            .catch(function (error) {
                console.error('Error crafting gear:', error);
                showToast('Error crafting gear', 'error');
            });
        }
    );
}
