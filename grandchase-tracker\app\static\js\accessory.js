/**
 * GrandChase Tracker - Accessory JavaScript
 * Contains functionality for the Accessory Tracker tab
 */

// Update accessory for a character
function updateAccessory(characterId, accessoryType, accessoryValue) {
    axios.post('/update_accessory', {
        character_id: characterId,
        accessory_type: accessoryType,
        accessory_value: accessoryValue
    })
    .then(function (response) {
        if (response.data.success) {
            console.log(`${accessoryType} updated successfully`);
            
            // Update the icon dynamically
            const iconElement = document.getElementById(`${accessoryType}-icon-${characterId}`);
            if (iconElement) {
                const imagePath = `images/accessories/${accessoryType}s/${accessoryValue.replace(/ /g, '_')}.png`;
                iconElement.src = `/static/${imagePath}`;
            }
            
            showToast(`Updated ${accessoryType} for character #${characterId}`);
        } else {
            console.error(`Failed to update ${accessoryType}:`, response.data.error);
            showToast(`Error updating ${accessoryType}`, 'error');
        }
    })
    .catch(function (error) {
        console.error(`Error updating ${accessoryType}:`, error);
        showToast(`Error updating ${accessoryType}`, 'error');
    });
}
