@echo off
title Building Fixed GrandChase Tracker Executable (Version 3)
color 0B

echo.
echo  ========================================
echo    Building Fixed GrandChase Tracker Executable (Version 3)
echo  ========================================
echo.
echo  This script will build a new executable with the following fixes:
echo  1. Void Tracker checkbox status now saved on refresh
echo  2. ToD Tracker checkbox status now saved on refresh (completely rewritten)
echo.
echo  Changes in this version:
echo  - Added direct IDs to ToD checkboxes for easier targeting
echo  - Created a dedicated function to initialize ToD checkboxes
echo  - Completely rewrote the checkbox state loading logic
echo  - Added direct AJAX calls to get ToD data for each character
echo.

:: Change to the directory where the batch file is located
cd /d "%~dp0"

:: Check if PyInstaller is installed
python -c "import PyInstaller" >nul 2>&1
if %errorlevel% neq 0 (
    color 0C
    echo ERROR: PyInstaller is not installed.
    echo Installing PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo Failed to install PyInstaller. Please install it manually with:
        echo pip install pyinstaller
        pause
        exit /b 1
    )
)

:: Check if database exists and rename it temporarily
if exist database.db (
    echo Backing up existing database...
    ren database.db database.db.bak
)

:: Build the executable
echo Building executable (this may take a few minutes)...
echo.
pyinstaller --clean GrandChase_Tracker.spec

:: Restore the database if it was backed up
if exist database.db.bak (
    echo Restoring database...
    if exist dist\GrandChase_Tracker.exe (
        if exist dist\database.db (
            del dist\database.db
        )
    )
    ren database.db.bak database.db
)

:: Check if build was successful
if exist dist\GrandChase_Tracker.exe (
    echo.
    echo  ========================================
    echo    Build Successful!
    echo  ========================================
    echo.
    echo The fixed executable has been created at:
    echo %CD%\dist\GrandChase_Tracker.exe
    echo.
    echo You can now run prepare_for_sharing.bat to create a distribution package.
    echo.
) else (
    color 0C
    echo.
    echo  ========================================
    echo    Build Failed!
    echo  ========================================
    echo.
    echo Please check the error messages above.
    echo.
)

pause
