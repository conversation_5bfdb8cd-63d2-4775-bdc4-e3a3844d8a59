"""
GrandChase Tracker - Configuration Settings
"""
import os

class Config:
    """Base configuration class"""
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-for-grandchase-tracker'

    # Database settings
    DATABASE_PATH = os.environ.get('DATABASE_PATH') or 'database.db'

    # Application settings
    CHARACTERS = [
        "Elesis", "Lire", "<PERSON><PERSON>", "<PERSON>s", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
        "<PERSON><PERSON>", "<PERSON><PERSON>", "Veigas", "<PERSON>anee", "<PERSON>", "Kallia", "Uno"
    ]

    # Reset time settings (3 AM in -3 GMT, which is 6 AM UTC)
    RESET_HOUR_UTC = 6

    # Gear crafting settings
    FRAGMENT_REDUCTION_PER_CRAFT = 60

    # Accessory options
    RING_OPTIONS = [
        "Unkeepable Promise III", "Shining Ring of Infinity III", "Forged Ring of Infinity III",
        "Faded Ring of Infinity III", "Shining Ring of Dimension III", "Forged Ring of Dimension III",
        "Faded Ring of Dimension III", "Unkeepable Promise II", "Shining Ring of Infinity II",
        "Forged Ring of Infinity II", "Faded Ring of Infinity II", "Shining Ring of Dimension II",
        "Forged Ring of Dimension II", "Faded Ring of Dimension II", "Unkeepable Promise I",
        "Shining Ring of Infinity I", "Forged Ring of Infinity I", "Faded Ring of Infinity I",
        "Shining Ring of Dimension I", "Forged Ring of Dimension I", "Faded Ring of Dimension I",
        "Harkyon"
    ]

    EARRING_OPTIONS = [
        "Chaos", "Relic Dimensional", "Epic Dimensional", "Rare Dimensional", "Order"
    ]

    PIERCING_OPTIONS = [
        "Chaos", "Relic Dimensional", "Epic Dimensional", "Rare Dimensional", "Order"
    ]

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False

# Set the active configuration
config = DevelopmentConfig
