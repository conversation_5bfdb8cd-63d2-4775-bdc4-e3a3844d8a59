@echo off
title Testing GrandChase Tracker Fixes
color 0B

echo.
echo  ========================================
echo    Testing GrandChase Tracker Fixes
echo  ========================================
echo.
echo  This script will run the application to test the fixes for:
echo  1. Void Tracker checkbox status not being saved on refresh
echo  2. ToD Tracker checkbox status not being saved on refresh
echo.
echo  Instructions:
echo  1. Check some boxes in the Void Tracker and ToD Tracker
echo  2. Refresh the page
echo  3. Verify that the checkbox states are preserved
echo.
echo  Press any key to start the application...
pause > nul

:: Change to the directory where the batch file is located
cd /d "%~dp0"

:: Run the application
python main.py

echo.
echo  Application has stopped.
echo  Did the fixes work? If not, please let me know what issues remain.
echo.
pause
