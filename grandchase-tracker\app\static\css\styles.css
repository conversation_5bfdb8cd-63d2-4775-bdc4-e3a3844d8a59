/* GrandChase Tracker - Main Stylesheet */

/* Variables */
:root {
    /* Color palette */
    --bg-color: #f8f9fa;
    --text-color: #333;
    --primary-color: #4e54c8;
    --primary-color-rgb: 78, 84, 200; /* RGB values for primary color */
    --primary-hover: #3d41aa;
    --secondary-color: #6c63ff;
    --accent-color: #8854d0;
    --success-color: #20bf6b;
    --error-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --table-bg: white;
    --table-border: #eaeaea;
    --table-header: #f5f5f5;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;

    /* Border radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
}

/* Dark mode variables */
body.dark-mode {
    --bg-color: #121212;
    --text-color: #e0e0e0;
    --primary-color: #2eba6f;
    --primary-color-rgb: 46, 186, 111; /* RGB values for primary color in dark mode */
    --primary-hover: #2e3cba;
    --secondary-color: #51a8db;
    --accent-color: #9d6eff;
    --success-color: #2eba6f;
    --table-bg: #1e1e1e;
    --table-border: #333;
    --table-header: #2a2a2a;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.4);
}

/* Base styles */
body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: var(--transition);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.header h1 {
    margin: 0;
    font-size: 2rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.header h1 i {
    color: var(--primary-color);
}

/* Dark mode toggle */
.dark-mode-toggle {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: var(--transition);
}

.dark-mode-toggle:hover {
    background-color: var(--primary-hover);
}

/* Controls */
.controls {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.controls button {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
}

.controls button:hover {
    background-color: var(--primary-hover);
}

/* Tabs */
.tabs {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.tab-button {
    background-color: var(--table-bg);
    color: var(--text-color);
    border: 1px solid var(--table-border);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
}

.tab-button:hover {
    background-color: var(--table-header);
}

.tab-button.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Tab content */
.tab-content {
    display: none;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-lg);
    background-color: var(--table-bg);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

thead {
    background-color: var(--table-header);
}

th, td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--table-border);
}

th {
    font-weight: 600;
}

tr:last-child td {
    border-bottom: none;
}

/* Character icon */
.character-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: var(--spacing-sm);
    vertical-align: middle;
}

/* Gear icon */
.gear-icon {
    width: 24px;
    height: 24px;
    vertical-align: middle;
    margin-right: var(--spacing-xs);
}

/* Void run counts */
.void-run-counts {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.void-counter {
    background-color: var(--table-bg);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    text-align: center;
    font-weight: 600;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 60px;
    border: 2px solid transparent;
}

.void-counter::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

/* Enhanced complete state with celebration effect */
.void-counter.complete {
    background: linear-gradient(135deg, var(--success-color), #4ade80);
    color: white;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
    animation: celebrate 0.6s ease-out;
}

@keyframes celebrate {
    0% { transform: translateY(-3px) scale(1.02); }
    25% { transform: translateY(-5px) scale(1.05); }
    50% { transform: translateY(-3px) scale(1.02); }
    75% { transform: translateY(-4px) scale(1.03); }
    100% { transform: translateY(-3px) scale(1.02); }
}

/* Enhanced partial state */
.void-counter.partial {
    background: linear-gradient(135deg, var(--warning-color), #fbbf24);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

.void-counter.partial::after {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), var(--warning-color));
    opacity: 0.6;
}

/* Enhanced empty state */
.void-counter.empty {
    background-color: var(--table-bg);
    color: var(--text-color);
    opacity: 0.8;
    transform: none;
}

.void-counter.empty::after {
    background: linear-gradient(90deg, #6b7280, #9ca3af);
    opacity: 0.4;
}

/* Enhanced Progress bar with animations */
.progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 6px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transition: width 1.2s cubic-bezier(0.22, 1, 0.36, 1);
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.15);
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
    overflow: hidden;
}

/* Animated shimmer effect */
.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Pulsing effect for complete state */
.void-counter.complete .progress-bar {
    background: linear-gradient(90deg, var(--success-color), #4ade80);
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);
    animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
    0%, 100% {
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);
        transform: scaleY(1);
    }
    50% {
        box-shadow: 0 0 30px rgba(34, 197, 94, 0.6);
        transform: scaleY(1.1);
    }
}

/* Enhanced partial state */
.void-counter.partial .progress-bar {
    background: linear-gradient(90deg, var(--warning-color), #fbbf24);
    box-shadow: 0 0 15px rgba(245, 158, 11, 0.3);
}

/* Empty state with subtle animation */
.void-counter.empty .progress-bar {
    background: linear-gradient(90deg, #6b7280, #9ca3af);
    box-shadow: 0 0 8px rgba(107, 114, 128, 0.2);
}

/* Percentage text */
.percentage-text {
    position: absolute;
    bottom: 8px;
    right: 8px;
    font-size: 0.8rem;
    opacity: 0.8;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    animation: fade-in 0.5s ease;
}

@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 0.8;
        transform: translateY(0);
    }
}

/* Celebration particle animation */
@keyframes celebrate-particle {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    50% {
        transform: translate(var(--end-x), var(--end-y)) scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: translate(var(--end-x), var(--end-y)) scale(0);
        opacity: 0;
    }
}

/* Enhanced celebrating state */
.void-counter.celebrating {
    animation: celebrate 0.6s ease-out, pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
    }
    50% {
        box-shadow: 0 8px 35px rgba(34, 197, 94, 0.5), 0 0 20px rgba(34, 197, 94, 0.3);
    }
}

/* Enhanced checkbox animations */
input[type="checkbox"] {
    position: relative;
    transform: scale(1);
    transition: all 0.2s cubic-bezier(0.22, 1, 0.36, 1);
}

input[type="checkbox"]:checked {
    animation: checkbox-check 0.3s ease-out;
}

@keyframes checkbox-check {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Enhanced table row animations */
tr[data-character-id] {
    transition: all 0.3s ease;
}

tr[data-character-id]:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    transform: translateX(2px);
}

/* Void checkbox container */
.void-checkbox-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.void-checkbox-container select {
    padding: 4px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--table-border);
    background-color: var(--table-bg);
    color: var(--text-color);
    transition: all 0.2s ease;
}

.void-checkbox-container select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

/* Shard display */
.shard-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
}

.shard-value-display {
    font-weight: 600;
    padding: 4px 8px;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-radius: var(--radius-sm);
    min-width: 30px;
    text-align: center;
}

.edit-shard-btn {
    background: none;
    border: none;
    color: var(--text-color);
    opacity: 0.5;
    cursor: pointer;
    transition: var(--transition);
    padding: 4px;
}

.edit-shard-btn:hover {
    opacity: 1;
    color: var(--primary-color);
}

.shard-edit-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    position: absolute;
    left: 0;
    top: 0;
    background-color: var(--table-bg);
    padding: 2px;
    border-radius: var(--radius-sm);
    box-shadow: var(--card-shadow);
    z-index: 10;
    animation: fade-in 0.2s ease;
}

.save-shard-btn {
    background: none;
    border: none;
    color: var(--success-color);
    cursor: pointer;
    transition: var(--transition);
    padding: 4px;
}

.save-shard-btn:hover {
    transform: scale(1.1);
}

/* Shard inputs */
.shard-value {
    width: 60px;
    padding: 4px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--table-border);
    background-color: var(--table-bg);
    color: var(--text-color);
}

/* Action container */
.action-container {
    position: relative;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.action-toggle-btn {
    padding: 6px 12px;
    border-radius: var(--radius-sm);
    border: none;
    background-color: var(--secondary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    width: 100%;
}

.action-toggle-btn:hover {
    background-color: var(--primary-hover);
}

.action-dropdown {
    position: absolute;
    right: 0;
    top: 100%;
    background-color: var(--table-bg);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    box-shadow: var(--card-shadow);
    z-index: 100;
    min-width: 200px;
    animation: fade-in 0.2s ease;
}

/* Convert container */
.convert-inputs {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.convert-input {
    width: 40px;
    padding: 4px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--table-border);
    background-color: var(--table-bg);
    color: var(--text-color);
}

.convert-button {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    border: none;
    background-color: var(--secondary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
}

.convert-button:hover {
    background-color: var(--primary-hover);
}

/* Conversion confirmation dialog */
.conversion-confirm {
    padding: var(--spacing-md);
    max-width: 400px;
}

.conversion-confirm h3 {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    text-align: center;
    font-size: 1.2rem;
}

.conversion-details {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.conversion-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.conversion-row:last-child {
    margin-bottom: 0;
}

.conversion-row .arrow {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.2rem;
}

.conversion-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-sm);
    border-top: 1px solid rgba(var(--primary-color-rgb), 0.2);
    font-weight: bold;
}

.conversion-ratio {
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.8;
}

.conversion-ratio p {
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
}

.conversion-ratio ul {
    margin: 0;
    padding-left: var(--spacing-md);
}

/* TA input */
.ta-input-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    justify-content: center;
}

.ta-input {
    width: 100px;
    padding: 6px 8px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--table-border);
    background-color: var(--table-bg);
    color: var(--text-color);
    font-size: 0.9rem;
    text-align: right;
    font-weight: 500;
}

.ta-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.3);
}

.save-ta-btn {
    padding: 6px 8px;
    border-radius: var(--radius-sm);
    border: none;
    background-color: var(--secondary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    font-size: 0.9rem;
}

.save-ta-btn:hover {
    background-color: var(--primary-hover);
}

.save-ta-btn:active {
    transform: translateY(1px);
}

/* TA controls and settings */
.ta-controls {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.ta-settings-container {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background-color: var(--table-bg);
    border-radius: var(--radius-md);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--table-border);
}

.settings-header {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--table-border);
    color: var(--primary-color);
    font-size: 1.2rem;
}

.ta-tier-list-toggle {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    border: none;
    background-color: var(--secondary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.ta-tier-list-toggle:hover {
    background-color: var(--primary-hover);
}

.ta-tier-list-toggle i {
    color: gold;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
}

.ta-settings {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    background-color: rgba(0, 0, 0, 0.1);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
}

.ta-settings label {
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    display: block;
}

.ta-tier-list-position,
.ta-tier-list-tabs {
    margin-bottom: var(--spacing-sm);
}

.checkbox-container,
.radio-container,
.tab-settings-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.tab-settings-container {
    flex-direction: column;
    max-height: 300px;
    overflow-y: auto;
    background-color: rgba(0, 0, 0, 0.05);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.tab-setting-item {
    display: flex;
    flex-direction: column;
    background-color: var(--table-bg);
    border-radius: var(--radius-sm);
    border: 1px solid var(--table-border);
    overflow: hidden;
}

.tab-setting-header {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 10px;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-bottom: 1px solid var(--table-border);
}

.tab-position-selector {
    display: flex;
    padding: 4px;
    gap: 8px;
}

.checkbox-item,
.radio-item {
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: var(--table-bg);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--table-border);
}

.radio-item.small {
    padding: 2px 6px;
    font-size: 0.85rem;
}

.checkbox-item input[type="checkbox"],
.radio-item input[type="radio"] {
    margin: 0;
}

.checkbox-item label,
.radio-item label {
    margin: 0;
    font-weight: normal;
    cursor: pointer;
}

.checkbox-item:hover,
.radio-item:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

/* Global TA tier list */
.global-ta-tier-list {
    margin-bottom: var(--spacing-lg);
    position: relative;
    z-index: 10;
}

.ta-tier-list {
    margin-bottom: var(--spacing-lg);
    background-color: #2a2a2a;
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.ta-tier-title {
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 1.2rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.ta-tier-title i {
    color: gold;
}

.ta-tiers-container {
    display: flex;
    flex-direction: column;
}

.ta-tier-row {
    display: flex;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: var(--spacing-xs) 0;
    margin-bottom: var(--spacing-xs);
    transition: all 0.2s ease;
}

.ta-tier-row:last-child {
    border-bottom: none;
}

.ta-tier-row:hover {
    transform: translateX(2px);
}

.ta-tier-label {
    min-width: 80px;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: #333;
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: var(--spacing-xs);
    border-radius: var(--radius-sm);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.ta-character-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs);
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.ta-tier-row:hover .ta-character-list {
    border-left-color: inherit;
    background-color: rgba(0, 0, 0, 0.02);
}

.ta-character-item {
    display: flex;
    align-items: center;
    gap: 6px;
    background-color: rgba(255, 255, 255, 0.15);
    padding: 6px 12px;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ta-character-item:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
}

.ta-character-item .character-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.ta-character-name {
    font-weight: 600;
    margin-right: 4px;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
    color: rgba(255, 255, 255, 0.95);
    font-size: 1rem;
}

.ta-character-score {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 600;
    background: none;
    padding: 0;
    margin-left: 4px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.average-ta-section {
    text-align: center;
    padding: var(--spacing-sm);
    background: linear-gradient(90deg, #2a2a2a, #333);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
}

#average-ta-display {
    color: white;
    font-size: 1.2rem;
    margin: 0;
}

.character-cell {
    display: flex;
    align-items: center;
}

.average-ta-section {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background-color: var(--table-bg);
    border-radius: var(--radius-md);
    box-shadow: var(--card-shadow);
}

/* Gear counts */
.gear-counts {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

/* Use the same styling as void counters */
.gear-counts .void-counter {
    background-color: var(--table-bg);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    text-align: center;
    font-weight: 600;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

/* Craft buttons */
.craft-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.craft-button {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    border: none;
    background-color: var(--secondary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
}

.craft-button:hover {
    background-color: var(--primary-hover);
}

/* Abyss run counts */
.abyss-run-counts {
    grid-template-columns: 1fr;
}

/* ToD run counts */
.tod-run-counts {
    grid-template-columns: 1fr;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-lg);
}

/* ToD date display */
.tod-date {
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.7;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    padding: 4px 10px;
    border-radius: var(--radius-md);
    font-weight: 500;
}

/* Enhanced ToD counter specific styles */
.tod-run-counts .void-counter {
    font-size: 1.2rem;
    padding: var(--spacing-lg);
    min-width: 200px;
    min-height: 90px;
    font-weight: 700;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
    position: relative;
    overflow: hidden;
}

.tod-run-counts .void-counter::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.tod-run-counts .void-counter:hover::before {
    left: 100%;
}

.tod-run-counts .void-counter.complete {
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 12px 30px rgba(34, 197, 94, 0.25);
}

.tod-run-counts .void-counter.partial {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 10px 25px rgba(245, 158, 11, 0.2);
}

.tod-run-counts .void-counter:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.tod-run-counts .percentage-text {
    font-size: 1rem;
    bottom: 12px;
    right: 12px;
}

/* Preference container */
.preference-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background-color: var(--table-bg);
    border-radius: var(--radius-md);
    box-shadow: var(--card-shadow);
}

.preference-container select {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--table-border);
    background-color: var(--table-bg);
    color: var(--text-color);
}

.apply-to-all {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    border: none;
    background-color: var(--secondary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    margin-left: auto;
}

.apply-to-all:hover {
    background-color: var(--primary-hover);
}

/* ToD Drop Toggles */
.drop-floor {
    font-size: 0.85rem;
    padding: 4px;
    font-weight: 600;
    text-align: center;
}

/* 5F header style */
.drop-floor[style*="4e54c8"] {
    background-color: rgba(78, 84, 200, 0.15);
    border-bottom: 2px solid #4e54c8;
}

/* 10F header style */
.drop-floor[style*="f7b733"] {
    background-color: rgba(247, 183, 51, 0.15);
    border-bottom: 2px solid #f7b733;
}

.drop-cell {
    text-align: center;
    padding: 4px;
}

.drop-toggle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 3px 8px;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    border: 1px solid transparent;
    min-width: 60px;
}

/* 5F specific styles */
.drop-toggle[id*="5f"] {
    background-color: rgba(78, 84, 200, 0.1);
    border-color: rgba(78, 84, 200, 0.3);
}

.drop-toggle[id*="5f"]:hover {
    background-color: rgba(78, 84, 200, 0.2);
}

/* 10F specific styles */
.drop-toggle[id*="10f"] {
    background-color: rgba(247, 183, 51, 0.1);
    border-color: rgba(247, 183, 51, 0.3);
}

.drop-toggle[id*="10f"]:hover {
    background-color: rgba(247, 183, 51, 0.2);
}

.drop-toggle input[type="checkbox"] {
    margin: 0;
}

.drop-toggle label {
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
}

/* 5F label color */
.drop-toggle[id*="5f"] label {
    color: #4e54c8;
}

/* 10F label color */
.drop-toggle[id*="10f"] label {
    color: #f7b733;
}

/* Checked state */
.drop-toggle input[type="checkbox"]:checked + label {
    font-weight: 700;
}

/* Checked state background */
.drop-toggle[id*="5f"] input[type="checkbox"]:checked + label {
    text-shadow: 0 0 1px rgba(78, 84, 200, 0.5);
}

.drop-toggle[id*="10f"] input[type="checkbox"]:checked + label {
    text-shadow: 0 0 1px rgba(247, 183, 51, 0.5);
}

/* Cleared checkbox styles */
.cleared-cell {
    text-align: center;
    padding: 4px;
}

.cleared-toggle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 4px 10px;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border: 1px solid rgba(var(--primary-color-rgb), 0.2);
    min-width: 80px;
}

.cleared-toggle:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.cleared-toggle input[type="checkbox"] {
    display: none;
}

.cleared-toggle label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    color: var(--text-color);
}

.cleared-toggle label i {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
    font-size: 0.7rem;
    transition: all 0.2s ease;
}

.cleared-toggle input[type="checkbox"]:not(:checked) + label i {
    opacity: 0.3;
}

.cleared-toggle input[type="checkbox"]:checked + label {
    font-weight: 700;
    color: var(--primary-color);
}

.cleared-toggle input[type="checkbox"]:checked + label i {
    background-color: var(--primary-color);
    color: white;
}

/* Cleared header style */
.cleared-header {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-bottom: 2px solid var(--primary-color);
    position: relative;
}

/* Dark mode adjustments for cleared toggle */
.dark-mode .cleared-toggle {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .cleared-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .cleared-toggle label i {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Settings Header */
.settings-header {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
    color: var(--primary-color);
}

.tod-settings {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.floor-preference {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Accessory selector */
.accessory-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.accessory-selector select {
    padding: 4px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--table-border);
    background-color: var(--table-bg);
    color: var(--text-color);
}

.accessory-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

/* Date range selector */
.date-range-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background-color: var(--table-bg);
    border-radius: var(--radius-md);
    box-shadow: var(--card-shadow);
    flex-wrap: wrap;
}

.date-range-selector input[type="date"] {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--table-border);
    background-color: var(--table-bg);
    color: var(--text-color);
}

.date-range-selector button {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    border: none;
    background-color: var(--secondary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
}

.date-range-selector button:hover {
    background-color: var(--primary-hover);
}

/* ToD History Styles */

/* Summary Cards */
.tod-summary-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.summary-card {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    background-color: var(--table-bg);
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    overflow: hidden;
    position: relative;
}

.summary-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.summary-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
}

.runs-card::after {
    background: linear-gradient(to bottom, #4e54c8, #8f94fb);
}

.drops-card::after {
    background: linear-gradient(to bottom, #f7b733, #fc4a1a);
}

.rate-card::after {
    background: linear-gradient(to bottom, #11998e, #38ef7d);
}

.best-floor-card::after {
    background: linear-gradient(to bottom, #8e2de2, #4a00e0);
}

.card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: var(--spacing-md);
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
    font-size: 1.5rem;
}

.runs-card .card-icon {
    background-color: rgba(78, 84, 200, 0.1);
    color: #4e54c8;
}

.drops-card .card-icon {
    background-color: rgba(247, 183, 51, 0.1);
    color: #f7b733;
}

.rate-card .card-icon {
    background-color: rgba(17, 153, 142, 0.1);
    color: #11998e;
}

.best-floor-card .card-icon {
    background-color: rgba(142, 45, 226, 0.1);
    color: #8e2de2;
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 0.85rem;
    color: var(--text-color);
    opacity: 0.7;
    margin-bottom: 4px;
}

.card-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
}

.runs-card .card-value {
    color: #4e54c8;
}

.drops-card .card-value {
    color: #f7b733;
}

.rate-card .card-value {
    color: #11998e;
}

.best-floor-card .card-value {
    color: #8e2de2;
}

/* Detailed Stats */
.tod-stats-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stats-section {
    background-color: var(--table-bg);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    margin-bottom: var(--spacing-md);
}

.section-header {
    padding: var(--spacing-md);
    font-weight: 600;
    color: white;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.run-stats-header {
    background: linear-gradient(90deg, #4e54c8, #8f94fb);
}

.drop-analysis-header {
    background: linear-gradient(90deg, #f7b733, #fc4a1a);
}

.drop-rates-header {
    background: linear-gradient(90deg, #11998e, #38ef7d);
}

.stats-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--table-border);
}

.stats-row:last-child {
    border-bottom: none;
}

.stats-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
}

.stats-label i {
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.stats-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* Drop Rate Comparison */
.drop-rate-comparison {
    background-color: var(--table-bg);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    margin-bottom: var(--spacing-lg);
}

.comparison-header {
    padding: var(--spacing-md);
    font-weight: 600;
    color: white;
    background: linear-gradient(90deg, #8e2de2, #4a00e0);
}

.comparison-bars {
    padding: var(--spacing-md);
}

.comparison-row {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.comparison-row:last-child {
    margin-bottom: 0;
}

.comparison-label {
    width: 150px;
    font-weight: 500;
}

.comparison-bar-container {
    flex: 1;
    height: 24px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.comparison-bar {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 var(--spacing-sm);
    color: white;
    font-weight: 500;
    font-size: 0.85rem;
    transition: width 1s cubic-bezier(0.22, 1, 0.36, 1);
    white-space: nowrap;
}

[id="5f-5f-bar"] {
    background: linear-gradient(90deg, #4e54c8, #8f94fb);
}

[id="5f-10f-bar"] {
    background: linear-gradient(90deg, #f7b733, #fc4a1a);
}

[id="10f-bar"] {
    background: linear-gradient(90deg, #8e2de2, #4a00e0);
}

/* Toast notifications */
#toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    padding: 12px 20px;
    border-radius: var(--radius-md);
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    animation: slide-in 0.3s ease;
    max-width: 300px;
}

.toast.success {
    background-color: var(--success-color);
}

.toast.error {
    background-color: var(--error-color);
}

.toast.warning {
    background-color: var(--warning-color);
}

.toast.info {
    background-color: var(--info-color);
}

.toast.fade-out {
    animation: fade-out 0.3s ease forwards;
}

@keyframes slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fade-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Modal dialog */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--table-bg);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--card-shadow);
    max-width: 500px;
    width: 90%;
}

.modal-message {
    margin-bottom: var(--spacing-md);
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.modal-button {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.modal-button.confirm {
    background-color: var(--primary-color);
    color: white;
}

.modal-button.confirm:hover {
    background-color: var(--primary-hover);
}

.modal-button.cancel {
    background-color: var(--table-header);
    color: var(--text-color);
}

.modal-button.cancel:hover {
    background-color: var(--table-border);
}

/* Overview Tab Styles */
.overview-summary {
    margin-bottom: var(--spacing-xl);
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    background-color: var(--table-bg);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: var(--spacing-md);
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
    font-size: 1.5rem;
}

.stat-content {
    flex: 1;
}

.stat-title {
    font-size: 0.85rem;
    color: var(--text-color);
    opacity: 0.7;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
}

/* Character Cards Grid */
.character-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.character-card {
    background-color: var(--table-bg);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    border: 2px solid transparent;
}

.character-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    border-color: rgba(var(--primary-color-rgb), 0.2);
}

.character-card-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--table-border);
}

.character-card-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    margin-right: var(--spacing-md);
    border: 3px solid rgba(var(--primary-color-rgb), 0.2);
}

.character-card-info {
    flex: 1;
}

.character-card-name {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.character-card-ta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.ta-label {
    font-weight: 600;
    color: var(--text-color);
}

.ta-card-input {
    width: 80px;
    padding: 4px 8px;
    border: 1px solid var(--table-border);
    border-radius: var(--radius-sm);
    font-size: 0.9rem;
}

.ta-card-save {
    padding: 4px 8px;
    border: none;
    border-radius: var(--radius-sm);
    background-color: var(--success-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
}

.ta-card-save:hover {
    background-color: #1a9954;
}

.character-card-content {
    margin-bottom: var(--spacing-lg);
}

.void-status-section, .abyss-status-section {
    margin-bottom: var(--spacing-md);
}

.void-status-row {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.void-status-item, .abyss-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    background-color: var(--table-header);
}

.void-label, .abyss-label {
    font-weight: 600;
    color: var(--text-color);
}

.void-status, .abyss-status {
    font-weight: 500;
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.85rem;
}

.void-status.completed, .abyss-status.completed {
    background-color: var(--success-color);
    color: white;
}

.void-status.incomplete, .abyss-status.incomplete {
    background-color: var(--error-color);
    color: white;
}

.shards-summary {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background-color: var(--table-header);
    border-radius: var(--radius-sm);
}

.shards-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.shards-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-color);
    opacity: 0.7;
}

.shards-value {
    font-weight: 600;
    color: var(--primary-color);
}

.character-card-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.action-row {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.action-btn {
    flex: 1;
    min-width: 100px;
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.action-btn i {
    font-size: 0.75rem;
}

.complete-btn {
    background-color: var(--success-color);
    color: white;
}

.complete-btn:hover {
    background-color: #1a9954;
}

.reset-btn {
    background-color: var(--error-color);
    color: white;
}

.reset-btn:hover {
    background-color: #c0392b;
}

.edit-btn {
    background-color: var(--secondary-color);
    color: white;
}

.edit-btn:hover {
    background-color: var(--primary-hover);
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-sm);
    }

    .header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .void-run-counts, .gear-counts {
        grid-template-columns: 1fr;
    }

    .tod-run-counts {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .tod-run-counts .void-counter {
        min-width: 100%;
    }

    .tod-summary-cards {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
    }

    .comparison-label {
        width: 120px;
    }

    .preference-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .apply-to-all {
        margin-left: 0;
        width: 100%;
        margin-top: var(--spacing-xs);
    }

    .date-range-selector {
        flex-direction: column;
        align-items: flex-start;
    }

    .date-range-selector button {
        width: 100%;
    }

    /* Overview responsive styles */
    .summary-stats {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
    }

    .character-cards-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .character-card {
        padding: var(--spacing-md);
    }

    .character-card-icon {
        width: 48px;
        height: 48px;
    }

    .action-row {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .action-btn {
        min-width: auto;
        width: 100%;
    }
}
