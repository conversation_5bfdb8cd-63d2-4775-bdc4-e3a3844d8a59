"""
GrandChase Tracker - Gear Routes
"""
from flask import Blueprint, request, jsonify, current_app
from app.models.database import get_db_connection

gear_bp = Blueprint('gear', __name__)

@gear_bp.route('/update_gear', methods=['POST'])
def update_gear():
    """Update gear status for a character"""
    data = request.get_json()
    character_id = data.get('character_id')
    gear_type = data.get('gear_type')
    checked = data.get('checked')

    # Validate gear type
    valid_gear_types = ['weapon', 'helmet', 'armor', 'pants', 'gloves', 'shoes', 'cloak']
    if gear_type not in valid_gear_types:
        return jsonify(success=False, error=f"Invalid gear type: {gear_type}")

    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Update gear status
            cursor.execute(f'''
                UPDATE gear
                SET {gear_type} = ?
                WHERE character_id = ?
            ''', (checked, character_id))

            # If no rows were updated, insert a new record
            if cursor.rowcount == 0:
                # Create a dictionary with all gear types set to 0
                gear_values = {gear: 0 for gear in valid_gear_types}
                # Update the specified gear type
                gear_values[gear_type] = checked

                cursor.execute('''
                    INSERT INTO gear (
                        character_id, weapon, helmet, armor, pants, gloves, shoes, cloak
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    character_id,
                    gear_values['weapon'],
                    gear_values['helmet'],
                    gear_values['armor'],
                    gear_values['pants'],
                    gear_values['gloves'],
                    gear_values['shoes'],
                    gear_values['cloak']
                ))

            conn.commit()

            return jsonify(success=True)
    except Exception as e:
        return jsonify(success=False, error=str(e))

@gear_bp.route('/get_gear_counts', methods=['GET'])
def get_gear_counts():
    """Get counts of gear by type"""
    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Count the number of characters with each gear type
            cursor.execute('''
                SELECT
                    SUM(weapon) as weapon_count,
                    SUM(helmet) as helmet_count,
                    SUM(armor) as armor_count,
                    SUM(pants) as pants_count,
                    SUM(gloves) as gloves_count,
                    SUM(shoes) as shoes_count,
                    SUM(cloak) as cloak_count
                FROM gear
            ''')

            result = cursor.fetchone()

            # Get total character count
            cursor.execute('SELECT COUNT(*) as total FROM characters')
            total_result = cursor.fetchone()
            total_characters = total_result['total']

            # Calculate void counts
            void1_count = result['weapon_count'] or 0
            void2_count = (result['helmet_count'] or 0) + (result['armor_count'] or 0) + (result['pants_count'] or 0)
            void3_count = (result['gloves_count'] or 0) + (result['shoes_count'] or 0) + (result['cloak_count'] or 0)

            return jsonify(
                success=True,
                void1_count=void1_count,
                void2_count=void2_count,
                void3_count=void3_count,
                total_characters=total_characters
            )
    except Exception as e:
        return jsonify(success=False, error=str(e))

@gear_bp.route('/craft_gear', methods=['POST'])
def craft_gear():
    """Craft gear using shards"""
    data = request.get_json()
    character_id = data.get('character_id')
    gear_type = data.get('gear_type')  # 'void1', 'void2', or 'void3'
    reduction = data.get('reduction', 60)  # Default to 60 fragments
    update_gear_db = data.get('update_gear_db', True)  # Whether to update the gear database

    # Map gear type to corresponding shard and gear columns
    gear_mappings = {
        'void1': {
            'shard_column': 'void1_shards',
            'gear_columns': ['weapon']
        },
        'void2': {
            'shard_column': 'void2_shards',
            'gear_columns': ['helmet', 'armor', 'pants']
        },
        'void3': {
            'shard_column': 'void3_shards',
            'gear_columns': ['gloves', 'shoes', 'cloak']
        }
    }

    if gear_type not in gear_mappings:
        return jsonify(success=False, error=f"Invalid gear type: {gear_type}")

    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()

            # Get current shard count
            shard_column = gear_mappings[gear_type]['shard_column']
            cursor.execute(f'''
                SELECT {shard_column} FROM shard_inventory WHERE character_id = ?
            ''', (character_id,))

            result = cursor.fetchone()

            if not result:
                return jsonify(success=False, error="Character not found in shard inventory")

            current_shards = result[shard_column]

            # Check if enough shards are available
            if current_shards < reduction:
                return jsonify(
                    success=False,
                    error=f"Not enough {gear_type} shards. Required: {reduction}, Available: {current_shards}"
                )

            # Update shard inventory
            new_shard_count = current_shards - reduction
            cursor.execute(f'''
                UPDATE shard_inventory
                SET {shard_column} = ?
                WHERE character_id = ?
            ''', (new_shard_count, character_id))

            # Update gear status if requested
            if update_gear_db:
                for gear_column in gear_mappings[gear_type]['gear_columns']:
                    cursor.execute(f'''
                        UPDATE gear
                        SET {gear_column} = 1
                        WHERE character_id = ?
                    ''', (character_id,))

            conn.commit()

            # Get updated shard counts
            cursor.execute('''
                SELECT void1_shards, void2_shards, void3_shards
                FROM shard_inventory
                WHERE character_id = ?
            ''', (character_id,))

            updated_shards = cursor.fetchone()

            return jsonify(
                success=True,
                void1_shards=updated_shards['void1_shards'],
                void2_shards=updated_shards['void2_shards'],
                void3_shards=updated_shards['void3_shards']
            )
    except Exception as e:
        return jsonify(success=False, error=str(e))
