/**
 * GrandChase Tracker - Gear JavaScript
 * Contains functionality for the Gear Tracker tab
 */

// Update gear status for a character
function updateGear(characterId, gearType, checked) {
    axios.post('/update_gear', {
        character_id: characterId,
        gear_type: gearType,
        checked: checked
    })
    .then(function (response) {
        if (response.data.success) {
            console.log('Gear updated successfully');
            updateGearCounts();
            showToast(`${gearType} gear ${checked ? 'added to' : 'removed from'} character #${characterId}`);
        } else {
            console.error('Failed to update gear:', response.data.error);
            showToast('Error updating gear status', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error updating gear:', error);
        showToast('Error updating gear status', 'error');
    });
}

// Update gear counts
function updateGearCounts() {
    axios.get('/get_gear_counts')
    .then(function (response) {
        if (response.data.success) {
            const void1Count = response.data.void1_count;
            const void2Count = response.data.void2_count;
            const void3Count = response.data.void3_count;
            const totalCharacters = response.data.total_characters;

            // Update counters with progress bars
            updateCounterWithProgressBar('void1-gear-count', void1Count, totalCharacters);
            updateCounterWithProgressBar('void2-gear-count', void2Count, totalCharacters * 3);
            updateCounterWithProgressBar('void3-gear-count', void3Count, totalCharacters * 3);
        } else {
            console.error('Failed to get gear counts:', response.data.error);
            showToast('Error getting gear counts', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error getting gear counts:', error);
        showToast('Error getting gear counts', 'error');
    });
}

// Craft gear for a character
function craftGear(characterId, gearType) {
    const fragmentReduction = 60; // Fragments reduced per craft

    // Custom confirmation modal
    Modal.confirm(
        `Are you sure you want to craft ${gearType.toUpperCase()} gear? This will reduce ${gearType.toUpperCase()} fragments by ${fragmentReduction}.`,
        function() {
            axios.post('/craft_gear', {
                character_id: characterId,
                gear_type: gearType,
                reduction: fragmentReduction
            })
            .then(function (response) {
                if (response.data.success) {
                    console.log(`${gearType} gear crafted successfully for character ${characterId}`);

                    // Update shard counts in the void tracker tab
                    updateShardCounts(characterId, response.data.void1_shards, response.data.void2_shards, response.data.void3_shards);

                    // Update gear checkboxes based on gear type
                    if (gearType === 'void1') {
                        const weaponCheckbox = document.querySelector(`tr[data-character-id="${characterId}"] input[onchange*="updateGear(${characterId}, 'weapon'"]`);
                        if (weaponCheckbox) weaponCheckbox.checked = true;
                    } else if (gearType === 'void2') {
                        const helmetCheckbox = document.querySelector(`tr[data-character-id="${characterId}"] input[onchange*="updateGear(${characterId}, 'helmet'"]`);
                        const armorCheckbox = document.querySelector(`tr[data-character-id="${characterId}"] input[onchange*="updateGear(${characterId}, 'armor'"]`);
                        const pantsCheckbox = document.querySelector(`tr[data-character-id="${characterId}"] input[onchange*="updateGear(${characterId}, 'pants'"]`);

                        if (helmetCheckbox) helmetCheckbox.checked = true;
                        if (armorCheckbox) armorCheckbox.checked = true;
                        if (pantsCheckbox) pantsCheckbox.checked = true;
                    } else if (gearType === 'void3') {
                        const glovesCheckbox = document.querySelector(`tr[data-character-id="${characterId}"] input[onchange*="updateGear(${characterId}, 'gloves'"]`);
                        const shoesCheckbox = document.querySelector(`tr[data-character-id="${characterId}"] input[onchange*="updateGear(${characterId}, 'shoes'"]`);
                        const cloakCheckbox = document.querySelector(`tr[data-character-id="${characterId}"] input[onchange*="updateGear(${characterId}, 'cloak'"]`);

                        if (glovesCheckbox) glovesCheckbox.checked = true;
                        if (shoesCheckbox) shoesCheckbox.checked = true;
                        if (cloakCheckbox) cloakCheckbox.checked = true;
                    }

                    // Update gear counts
                    updateGearCounts();

                    showToast(`Successfully crafted ${gearType} gear for character #${characterId}`);
                } else {
                    console.error('Failed to craft gear:', response.data.error);
                    showToast('Error crafting gear: ' + response.data.error, 'error');
                }
            })
            .catch(function (error) {
                console.error('Error crafting gear:', error);
                showToast('Error crafting gear', 'error');
            });
        }
    );
}

// Update shard counts in the void tracker tab
function updateShardCounts(characterId, void1Shards, void2Shards, void3Shards) {
    // Use the updateShardDisplays function from void.js if it exists
    if (typeof updateShardDisplays === 'function') {
        updateShardDisplays(characterId, {
            void1_shards: void1Shards,
            void2_shards: void2Shards,
            void3_shards: void3Shards
        });
    } else {
        // Fallback to direct update if the function doesn't exist
        const void1Display = document.querySelector(`tr[data-character-id="${characterId}"] .void1-shards .shard-value-display`);
        const void2Display = document.querySelector(`tr[data-character-id="${characterId}"] .void2-shards .shard-value-display`);
        const void3Display = document.querySelector(`tr[data-character-id="${characterId}"] .void3-shards .shard-value-display`);

        const void1Input = document.querySelector(`tr[data-character-id="${characterId}"] .void1-shards .shard-value`);
        const void2Input = document.querySelector(`tr[data-character-id="${characterId}"] .void2-shards .shard-value`);
        const void3Input = document.querySelector(`tr[data-character-id="${characterId}"] .void3-shards .shard-value`);

        if (void1Display) void1Display.textContent = void1Shards;
        if (void2Display) void2Display.textContent = void2Shards;
        if (void3Display) void3Display.textContent = void3Shards;

        if (void1Input) void1Input.value = void1Shards;
        if (void2Input) void2Input.value = void2Shards;
        if (void3Input) void3Input.value = void3Shards;
    }
}
