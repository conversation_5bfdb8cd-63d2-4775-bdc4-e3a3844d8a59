('C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
 '(8)\\grandchase-tracker\\build\\GrandChase_Tracker\\GrandChase_Tracker.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\build\\GrandChase_Tracker\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\build\\GrandChase_Tracker\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\build\\GrandChase_Tracker\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\build\\GrandChase_Tracker\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\build\\GrandChase_Tracker\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\build\\GrandChase_Tracker\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\main.py',
   'PYSOURCE'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('app\\static\\css\\style.css',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\css\\style.css',
   'DATA'),
  ('app\\static\\css\\styles.css',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\css\\styles.css',
   'DATA'),
  ('app\\static\\images\\accessories\\earrings\\Chaos.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\earrings\\Chaos.png',
   'DATA'),
  ('app\\static\\images\\accessories\\earrings\\Epic_Dimensional.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\earrings\\Epic_Dimensional.png',
   'DATA'),
  ('app\\static\\images\\accessories\\earrings\\Order.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\earrings\\Order.png',
   'DATA'),
  ('app\\static\\images\\accessories\\earrings\\Rare_Dimensional.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\earrings\\Rare_Dimensional.png',
   'DATA'),
  ('app\\static\\images\\accessories\\earrings\\Relic_Dimensional.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\earrings\\Relic_Dimensional.png',
   'DATA'),
  ('app\\static\\images\\accessories\\piercings\\Chaos.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\piercings\\Chaos.png',
   'DATA'),
  ('app\\static\\images\\accessories\\piercings\\Epic_Dimensional.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\piercings\\Epic_Dimensional.png',
   'DATA'),
  ('app\\static\\images\\accessories\\piercings\\Order.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\piercings\\Order.png',
   'DATA'),
  ('app\\static\\images\\accessories\\piercings\\Rare_Dimensional.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\piercings\\Rare_Dimensional.png',
   'DATA'),
  ('app\\static\\images\\accessories\\piercings\\Relic_Dimensional.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\piercings\\Relic_Dimensional.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Faded_Ring_of_Dimension_I.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Faded_Ring_of_Dimension_I.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Faded_Ring_of_Dimension_III.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Faded_Ring_of_Dimension_III.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Faded_Ring_of_Infinity_I.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Faded_Ring_of_Infinity_I.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Faded_Ring_of_Infinity_II.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Faded_Ring_of_Infinity_II.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Faded_Ring_of_Infinity_III.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Faded_Ring_of_Infinity_III.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Forged_Ring_of_Dimension_III.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Forged_Ring_of_Dimension_III.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Forged_Ring_of_Infinity_I.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Forged_Ring_of_Infinity_I.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Forged_Ring_of_Infinity_II.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Forged_Ring_of_Infinity_II.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Forged_Ring_of_Infinity_III.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Forged_Ring_of_Infinity_III.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Shining_Ring_of_Dimension_III.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Shining_Ring_of_Dimension_III.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Shining_Ring_of_Infinity_I.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Shining_Ring_of_Infinity_I.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Shining_Ring_of_Infinity_II.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Shining_Ring_of_Infinity_II.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Shining_Ring_of_Infinity_III.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Shining_Ring_of_Infinity_III.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Unkeepable_Promise_II.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Unkeepable_Promise_II.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\Unkeepable_Promise_III.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\Unkeepable_Promise_III.png',
   'DATA'),
  ('app\\static\\images\\accessories\\rings\\harkyon.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\accessories\\rings\\harkyon.png',
   'DATA'),
  ('app\\static\\images\\characters\\ai.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\ai.png',
   'DATA'),
  ('app\\static\\images\\characters\\amy.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\amy.png',
   'DATA'),
  ('app\\static\\images\\characters\\arme.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\arme.png',
   'DATA'),
  ('app\\static\\images\\characters\\asin.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\asin.png',
   'DATA'),
  ('app\\static\\images\\characters\\decanee.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\decanee.png',
   'DATA'),
  ('app\\static\\images\\characters\\dio.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\dio.png',
   'DATA'),
  ('app\\static\\images\\characters\\edel.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\edel.png',
   'DATA'),
  ('app\\static\\images\\characters\\elesis.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\elesis.png',
   'DATA'),
  ('app\\static\\images\\characters\\jin.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\jin.png',
   'DATA'),
  ('app\\static\\images\\characters\\kallia.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\kallia.png',
   'DATA'),
  ('app\\static\\images\\characters\\lass.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\lass.png',
   'DATA'),
  ('app\\static\\images\\characters\\ley.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\ley.png',
   'DATA'),
  ('app\\static\\images\\characters\\lime.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\lime.png',
   'DATA'),
  ('app\\static\\images\\characters\\lire.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\lire.png',
   'DATA'),
  ('app\\static\\images\\characters\\mari.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\mari.png',
   'DATA'),
  ('app\\static\\images\\characters\\rin.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\rin.png',
   'DATA'),
  ('app\\static\\images\\characters\\ronan.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\ronan.png',
   'DATA'),
  ('app\\static\\images\\characters\\rufus.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\rufus.png',
   'DATA'),
  ('app\\static\\images\\characters\\ryan.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\ryan.png',
   'DATA'),
  ('app\\static\\images\\characters\\sieghart.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\sieghart.png',
   'DATA'),
  ('app\\static\\images\\characters\\uno.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\uno.png',
   'DATA'),
  ('app\\static\\images\\characters\\veigas.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\veigas.png',
   'DATA'),
  ('app\\static\\images\\characters\\zero.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\characters\\zero.png',
   'DATA'),
  ('app\\static\\images\\gear\\armor_icon.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\gear\\armor_icon.png',
   'DATA'),
  ('app\\static\\images\\gear\\cape_icon.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\gear\\cape_icon.png',
   'DATA'),
  ('app\\static\\images\\gear\\cloak_icon.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\gear\\cloak_icon.png',
   'DATA'),
  ('app\\static\\images\\gear\\gloves_icon.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\gear\\gloves_icon.png',
   'DATA'),
  ('app\\static\\images\\gear\\helmet_icon.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\gear\\helmet_icon.png',
   'DATA'),
  ('app\\static\\images\\gear\\pants_icon.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\gear\\pants_icon.png',
   'DATA'),
  ('app\\static\\images\\gear\\shoes_icon.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\gear\\shoes_icon.png',
   'DATA'),
  ('app\\static\\images\\gear\\weapon_icon.png',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\images\\gear\\weapon_icon.png',
   'DATA'),
  ('app\\static\\js\\abyss.js',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\js\\abyss.js',
   'DATA'),
  ('app\\static\\js\\accessory.js',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\js\\accessory.js',
   'DATA'),
  ('app\\static\\js\\gear.js',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\js\\gear.js',
   'DATA'),
  ('app\\static\\js\\main.js',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\js\\main.js',
   'DATA'),
  ('app\\static\\js\\ta.js',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\js\\ta.js',
   'DATA'),
  ('app\\static\\js\\tod.js',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\js\\tod.js',
   'DATA'),
  ('app\\static\\js\\tod.js.new',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\js\\tod.js.new',
   'DATA'),
  ('app\\static\\js\\void.js',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\static\\js\\void.js',
   'DATA'),
  ('app\\templates\\base.html',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\templates\\base.html',
   'DATA'),
  ('app\\templates\\index.html',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\app\\templates\\index.html',
   'DATA'),
  ('config.py',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\config.py',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\click-8.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\click-8.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('click-8.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\click-8.2.0.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.0.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\click-8.2.0.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\click-8.2.0.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\Void - Copia '
   '(8)\\grandchase-tracker\\build\\GrandChase_Tracker\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
