#!/usr/bin/env python3
"""
GrandChase Tracker - Main Application Entry Point
"""
import os
import sys
import webbrowser
from threading import Timer
from waitress import serve

# Import Flask app
from app import create_app

def get_db_path():
    """Get the correct database path whether running as script or exe"""
    if getattr(sys, 'frozen', False):
        # If running as exe (frozen)
        application_path = os.path.dirname(sys.executable)
    else:
        # If running as script
        application_path = os.path.dirname(os.path.abspath(__file__))

    return os.path.join(application_path, 'database.db')

def open_browser():
    """Open the browser after a short delay"""
    Timer(1.5, lambda: webbrowser.open('http://localhost:5000')).start()

def run_app():
    """Run the Flask application using waitress"""
    try:
        # Create app instance with the correct database path
        app = create_app(get_db_path())

        # Open browser after a short delay
        open_browser()

        # Start the server
        print("Starting GrandChase Tracker...")
        print("The application will open in your default browser automatically.")
        print("If it doesn't open automatically, please go to: http://localhost:5000")
        print("To close the application, close this window.")
        serve(app, host='127.0.0.1', port=5000, threads=8, channel_timeout=300)

    except Exception as e:
        print(f"Error starting application: {str(e)}")
        input("Press Enter to exit...")  # Keep window open on error

if __name__ == '__main__':
    run_app()
