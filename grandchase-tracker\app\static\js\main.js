/**
 * GrandChase Tracker - Main JavaScript
 * Contains common functionality used across the application
 */

// Show a specific tab
function showTab(tabId) {
    // Hide all tab content
    document.querySelectorAll('.tab-content').forEach(tab => tab.style.display = 'none');

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => button.classList.remove('active'));

    // Show the selected tab
    document.getElementById(tabId).style.display = 'block';

    // Add active class to the clicked button
    document.querySelector(`.tab-button[onclick="showTab('${tabId}')"]`).classList.add('active');

    // Update specific counts when showing certain tabs
    if (tabId === 'gear-tracker') {
        updateGearCounts();
    } else if (tabId === 'abyss-tracker') {
        updateAbyssCount();
    } else if (tabId === 'tod-tracker') {
        updateToDCount();

        // Load ToD checkbox states from localStorage when switching to the ToD tab
        if (typeof loadToDCheckboxStatesFromLocalStorage === 'function') {
            loadToDCheckboxStatesFromLocalStorage();
        }
    } else if (tabId === 'void-tracker') {
        updateVoidRunCounts();
    }

    // Check if TA tier list should be shown on this tab
    if (typeof applyTASettings === 'function') {
        setTimeout(applyTASettings, 0); // Use setTimeout to ensure DOM is updated
    }
}

// Toggle dark mode
function toggleDarkMode() {
    const body = document.body;
    const isDarkMode = body.classList.toggle('dark-mode');

    // Save preference to localStorage
    localStorage.setItem('darkMode', isDarkMode);

    // Update the theme toggle button text
    const themeText = document.getElementById('theme-text');
    if (themeText) {
        themeText.textContent = isDarkMode ? 'Toggle Light Mode' : 'Toggle Dark Mode';
    }

    // Show toast notification
    showToast(`${isDarkMode ? 'Dark' : 'Light'} mode enabled`);
}

// Show toast notification
function showToast(message, type = 'success') {
    const toastContainer = document.getElementById('toast-container');

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Remove toast after 3 seconds
    setTimeout(() => {
        toast.classList.add('fade-out');
        setTimeout(() => {
            toastContainer.removeChild(toast);
        }, 300);
    }, 3000);
}

// Modal dialog
const Modal = {
    // Show confirmation dialog
    confirm: function(message, onConfirm) {
        // Create modal container
        const modalContainer = document.createElement('div');
        modalContainer.className = 'modal-container';

        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';

        // Create message
        const messageElement = document.createElement('div');
        messageElement.className = 'modal-message';
        messageElement.innerHTML = message; // Use innerHTML to support HTML content

        // Create buttons
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'modal-buttons';

        const confirmButton = document.createElement('button');
        confirmButton.className = 'modal-button confirm';
        confirmButton.textContent = 'Confirm';

        const cancelButton = document.createElement('button');
        cancelButton.className = 'modal-button cancel';
        cancelButton.textContent = 'Cancel';

        // Add event listeners
        confirmButton.addEventListener('click', () => {
            document.body.removeChild(modalContainer);
            if (typeof onConfirm === 'function') {
                onConfirm();
            }
        });

        cancelButton.addEventListener('click', () => {
            document.body.removeChild(modalContainer);
        });

        // Assemble modal
        buttonContainer.appendChild(confirmButton);
        buttonContainer.appendChild(cancelButton);

        modalContent.appendChild(messageElement);
        modalContent.appendChild(buttonContainer);

        modalContainer.appendChild(modalContent);

        // Add modal to body
        document.body.appendChild(modalContainer);
    }
};

// Update counter style based on completion
function updateCounterStyle(counterId, completedCount, totalCount) {
    const counter = document.getElementById(counterId);
    if (!counter) return;

    // Remove existing classes
    counter.classList.remove('complete', 'partial', 'empty');

    // Add appropriate class
    if (completedCount === 0) {
        counter.classList.add('empty');
    } else if (completedCount === totalCount) {
        counter.classList.add('complete');
    } else {
        counter.classList.add('partial');
    }
}

// Enhanced counter update with animations
function updateCounterWithProgressBar(counterId, completedCount, totalCount) {
    const counter = document.getElementById(counterId);
    if (!counter) return;

    // Store previous values for animation
    const previousText = counter.textContent;
    const previousCount = parseInt(previousText.match(/(\d+)\/\d+/)?.[1] || '0');

    // Update text with animated counting
    const counterText = counter.textContent.split(':')[0];

    // Animate the number change
    animateCounterText(counter, counterText, previousCount, completedCount, totalCount);

    // Update counter style with enhanced transitions
    updateCounterStyleEnhanced(counterId, completedCount, totalCount, previousCount);

    // Calculate percentage
    const percentage = (completedCount / totalCount) * 100;

    // Create or update progress bar with enhanced animations
    updateProgressBarEnhanced(counter, percentage, completedCount, totalCount);
}

// Animate counter text with counting effect
function animateCounterText(counter, counterText, fromCount, toCount, totalCount) {
    const duration = 800; // Animation duration in ms
    const startTime = performance.now();

    function updateText(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Use easing function for smooth animation
        const easedProgress = easeOutCubic(progress);
        const currentCount = Math.round(fromCount + (toCount - fromCount) * easedProgress);

        counter.textContent = `${counterText}: ${currentCount}/${totalCount}`;

        if (progress < 1) {
            requestAnimationFrame(updateText);
        }
    }

    requestAnimationFrame(updateText);
}

// Enhanced counter style updates with celebration effects
function updateCounterStyleEnhanced(counterId, completedCount, totalCount, previousCount) {
    const counter = document.getElementById(counterId);
    if (!counter) return;

    // Remove existing classes
    counter.classList.remove('complete', 'partial', 'empty', 'celebrating');

    // Add appropriate class
    if (completedCount === 0) {
        counter.classList.add('empty');
    } else if (completedCount === totalCount) {
        counter.classList.add('complete');

        // Add celebration effect if just completed
        if (previousCount < totalCount && completedCount === totalCount) {
            triggerCelebrationEffect(counter);
        }
    } else {
        counter.classList.add('partial');
    }
}

// Enhanced progress bar with smooth animations
function updateProgressBarEnhanced(counter, percentage, completedCount, totalCount) {
    // Create or update progress bar
    let progressBar = counter.querySelector('.progress-bar');
    if (!progressBar) {
        progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';
        counter.appendChild(progressBar);
    }

    // Animate progress bar width
    setTimeout(() => {
        progressBar.style.width = `${percentage}%`;
    }, 200);

    // Update percentage text with fade animation
    updatePercentageText(counter, percentage, completedCount);
}

// Update percentage text with smooth transitions
function updatePercentageText(counter, percentage, completedCount) {
    // Remove any existing percentage text
    const existingPercentageText = counter.querySelector('.percentage-text');
    if (existingPercentageText) {
        existingPercentageText.style.opacity = '0';
        setTimeout(() => {
            if (existingPercentageText.parentNode) {
                existingPercentageText.parentNode.removeChild(existingPercentageText);
            }
        }, 200);
    }

    // Only add percentage text if not empty
    if (completedCount > 0) {
        setTimeout(() => {
            const percentageText = document.createElement('span');
            percentageText.className = 'percentage-text';
            percentageText.textContent = `${Math.round(percentage)}%`;
            percentageText.style.opacity = '0';
            counter.appendChild(percentageText);

            // Fade in the new text
            setTimeout(() => {
                percentageText.style.opacity = '0.8';
            }, 50);
        }, 200);
    }
}

// Trigger celebration effect for completed counters
function triggerCelebrationEffect(counter) {
    counter.classList.add('celebrating');

    // Create celebration particles
    createCelebrationParticles(counter);

    // Remove celebration class after animation
    setTimeout(() => {
        counter.classList.remove('celebrating');
    }, 600);
}

// Create celebration particles effect
function createCelebrationParticles(counter) {
    const rect = counter.getBoundingClientRect();
    const particleCount = 8;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'celebration-particle';
        particle.style.cssText = `
            position: fixed;
            width: 6px;
            height: 6px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            left: ${rect.left + rect.width / 2}px;
            top: ${rect.top + rect.height / 2}px;
            animation: celebrate-particle 1s ease-out forwards;
            animation-delay: ${i * 50}ms;
        `;

        // Random direction for each particle
        const angle = (i / particleCount) * 2 * Math.PI;
        const distance = 50 + Math.random() * 30;
        particle.style.setProperty('--end-x', `${Math.cos(angle) * distance}px`);
        particle.style.setProperty('--end-y', `${Math.sin(angle) * distance}px`);

        document.body.appendChild(particle);

        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 1000);
    }
}

// Easing function for smooth animations
function easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
}

// Function to initialize ToD checkboxes from data attributes
function initializeToDCheckboxes() {
    console.log('Initializing ToD checkboxes from data attributes');

    // For each ToD checkbox, set its state from the data attribute
    document.querySelectorAll('input[id^="tod-cleared-"]').forEach(checkbox => {
        const characterId = checkbox.dataset.characterId;
        const isCleared = checkbox.dataset.isCleared === 'true';

        // Set the checkbox state
        checkbox.checked = isCleared;
        console.log(`Set ToD checkbox for character ${characterId} to ${isCleared}`);

        // Also update the floor select if it exists
        const floorSelect = document.getElementById(`floor-select-${characterId}`);
        if (floorSelect) {
            // Update drop toggles visibility
            if (typeof updateDropTogglesVisibility === 'function') {
                updateDropTogglesVisibility(characterId, parseInt(floorSelect.value));
            }
        }
    });
}

// Add loading animation to counters
function addLoadingAnimationToCounters() {
    const counters = document.querySelectorAll('.void-counter');
    counters.forEach((counter, index) => {
        counter.style.opacity = '0';
        counter.style.transform = 'translateY(20px)';
        counter.style.transition = 'all 0.6s cubic-bezier(0.22, 1, 0.36, 1)';

        setTimeout(() => {
            counter.style.opacity = '1';
            counter.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Add entrance animations to tables
function addEntranceAnimations() {
    const tables = document.querySelectorAll('table');
    tables.forEach((table, index) => {
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            row.style.opacity = '0';
            row.style.transform = 'translateX(-20px)';
            row.style.transition = 'all 0.4s cubic-bezier(0.22, 1, 0.36, 1)';

            setTimeout(() => {
                row.style.opacity = '1';
                row.style.transform = 'translateX(0)';
            }, (rowIndex * 50) + (index * 200));
        });
    });
}

// Initialize on page load with enhanced animations
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing application with animations');

    // Add loading animation to counters
    addLoadingAnimationToCounters();

    // Initialize ToD checkboxes
    initializeToDCheckboxes();

    // Show default tab
    showTab('void-tracker');

    // Initialize counters with staggered timing for smooth loading
    setTimeout(() => updateVoidRunCounts(), 200);
    setTimeout(() => updateGearCounts(), 400);
    setTimeout(() => updateToDCount(), 600);
    setTimeout(() => updateAbyssCount(), 800);

    // Add entrance animations to tables
    setTimeout(() => addEntranceAnimations(), 1000);

    // Set up dark mode toggle button
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', toggleDarkMode);
    }

    // Check for saved theme preference
    const savedDarkMode = localStorage.getItem('darkMode');
    if (savedDarkMode === 'true') {
        document.body.classList.add('dark-mode');

        // Update the theme toggle button text
        const themeText = document.getElementById('theme-text');
        if (themeText) {
            themeText.textContent = 'Toggle Light Mode';
        }
    } else if (savedDarkMode === null) {
        // Default to light mode if no preference is saved
        localStorage.setItem('darkMode', 'false');
        document.body.classList.remove('dark-mode');
    }

    console.log('Application initialized successfully with enhanced animations');
});
