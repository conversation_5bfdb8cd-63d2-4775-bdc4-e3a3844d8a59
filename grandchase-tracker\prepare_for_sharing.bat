@echo off
title Preparing GrandChase Tracker for Sharing
color 0B

echo.
echo  ========================================
echo    Preparing GrandChase Tracker for Sharing
echo  ========================================
echo.

:: Change to the directory where the batch file is located
cd /d "%~dp0"

:: Create a distribution folder
set DIST_FOLDER=GrandChase_Tracker_Distribution
echo Creating distribution folder...
if exist %DIST_FOLDER% (
    rmdir /s /q %DIST_FOLDER%
)
mkdir %DIST_FOLDER%

:: Copy the executable
echo Copying executable...
copy dist\GrandChase_Tracker.exe %DIST_FOLDER%\

:: Copy the README for your friend
echo Copying README...
copy FOR_YOUR_FRIEND.txt %DIST_FOLDER%\README.txt

echo.
echo  ========================================
echo    Distribution Package Ready!
echo  ========================================
echo.
echo Your distribution package has been created at:
echo %CD%\%DIST_FOLDER%
echo.
echo This folder contains:
echo - GrandChase_Tracker.exe (The standalone application)
echo - README.txt (Instructions for your friend)
echo.
echo You can now share this entire folder with your friend.
echo.

pause
