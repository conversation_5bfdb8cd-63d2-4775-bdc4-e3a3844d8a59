"""
GrandChase Tracker - Database Models and Initialization
"""
import os
import sqlite3
from datetime import datetime

def get_db_connection(db_path):
    """Create a database connection"""
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def init_db(db_path):
    """Initialize the database schema"""
    with get_db_connection(db_path) as conn:
        cursor = conn.cursor()

        # Create characters table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS characters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                image_path TEXT NOT NULL
            )
        ''')

        # Check if void_raids table exists and if it has the old structure
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='void_raids'")
        table_exists = cursor.fetchone()

        if table_exists:
            # Check if the table has the old structure (with date-based entries)
            cursor.execute("PRAGMA table_info(void_raids)")
            columns = cursor.fetchall()
            has_unique_constraint = False

            # Check if character_id has UNIQUE constraint by trying to get the schema
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='void_raids'")
            schema = cursor.fetchone()
            if schema and 'character_id INTEGER NOT NULL UNIQUE' in schema[0]:
                has_unique_constraint = True

            if not has_unique_constraint:
                # Migrate the old void_raids table to new structure
                print("Migrating void_raids table to new structure...")

                # Create new table with correct structure
                cursor.execute('''
                    CREATE TABLE void_raids_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        character_id INTEGER NOT NULL UNIQUE,
                        void1 BOOLEAN NOT NULL DEFAULT 0,
                        void2 BOOLEAN NOT NULL DEFAULT 0,
                        void3 BOOLEAN NOT NULL DEFAULT 0,
                        void1_clear_level INTEGER DEFAULT 3,
                        void2_clear_level INTEGER DEFAULT 3,
                        void3_clear_level INTEGER DEFAULT 3,
                        date TEXT NOT NULL,
                        FOREIGN KEY (character_id) REFERENCES characters(id)
                    )
                ''')

                # Migrate data - keep only the most recent entry for each character
                cursor.execute('''
                    INSERT INTO void_raids_new (character_id, void1, void2, void3, void1_clear_level, void2_clear_level, void3_clear_level, date)
                    SELECT character_id, void1, void2, void3, void1_clear_level, void2_clear_level, void3_clear_level, date
                    FROM void_raids
                    WHERE (character_id, date) IN (
                        SELECT character_id, MAX(date)
                        FROM void_raids
                        GROUP BY character_id
                    )
                ''')

                # Drop old table and rename new one
                cursor.execute('DROP TABLE void_raids')
                cursor.execute('ALTER TABLE void_raids_new RENAME TO void_raids')

                print("Migration completed.")
        else:
            # Create void_raids table with new structure
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS void_raids (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    character_id INTEGER NOT NULL UNIQUE,
                    void1 BOOLEAN NOT NULL DEFAULT 0,
                    void2 BOOLEAN NOT NULL DEFAULT 0,
                    void3 BOOLEAN NOT NULL DEFAULT 0,
                    void1_clear_level INTEGER DEFAULT 3,
                    void2_clear_level INTEGER DEFAULT 3,
                    void3_clear_level INTEGER DEFAULT 3,
                    date TEXT NOT NULL,
                    FOREIGN KEY (character_id) REFERENCES characters(id)
                )
            ''')

        # Create shard_inventory table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS shard_inventory (
                character_id INTEGER PRIMARY KEY,
                void1_shards INTEGER NOT NULL DEFAULT 0,
                void2_shards INTEGER NOT NULL DEFAULT 0,
                void3_shards INTEGER NOT NULL DEFAULT 0,
                FOREIGN KEY (character_id) REFERENCES characters(id)
            )
        ''')

        # Create ta_scores table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ta_scores (
                character_id INTEGER PRIMARY KEY,
                ta_score INTEGER NOT NULL DEFAULT 0,
                FOREIGN KEY (character_id) REFERENCES characters(id)
            )
        ''')

        # Create gear table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS gear (
                character_id INTEGER PRIMARY KEY,
                weapon BOOLEAN NOT NULL DEFAULT 0,
                helmet BOOLEAN NOT NULL DEFAULT 0,
                armor BOOLEAN NOT NULL DEFAULT 0,
                pants BOOLEAN NOT NULL DEFAULT 0,
                gloves BOOLEAN NOT NULL DEFAULT 0,
                shoes BOOLEAN NOT NULL DEFAULT 0,
                cloak BOOLEAN NOT NULL DEFAULT 0,
                FOREIGN KEY (character_id) REFERENCES characters(id)
            )
        ''')

        # Create abyss_runs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS abyss_runs (
                character_id INTEGER PRIMARY KEY,
                completed BOOLEAN NOT NULL DEFAULT 0,
                FOREIGN KEY (character_id) REFERENCES characters(id)
            )
        ''')

        # Create tod_runs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tod_runs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                character_id INTEGER NOT NULL,
                cleared BOOLEAN NOT NULL DEFAULT 0,
                spr_dropped INTEGER NOT NULL DEFAULT 0,
                floor INTEGER NOT NULL DEFAULT 10,
                date TEXT NOT NULL,
                detailed_data TEXT,
                FOREIGN KEY (character_id) REFERENCES characters(id)
            )
        ''')

        # Create accessories table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accessories (
                character_id INTEGER PRIMARY KEY,
                ring TEXT DEFAULT "Harkyon",
                earring TEXT DEFAULT "Order",
                piercing TEXT DEFAULT "Order",
                FOREIGN KEY (character_id) REFERENCES characters(id)
            )
        ''')

        conn.commit()

def init_characters(db_path, character_list):
    """Initialize the characters in the database"""
    today = datetime.now().strftime('%Y-%m-%d')

    with get_db_connection(db_path) as conn:
        cursor = conn.cursor()

        # Get existing characters
        cursor.execute('SELECT name FROM characters')
        existing_characters = {row['name'] for row in cursor.fetchall()}

        # Add new characters
        for character_name in character_list:
            if character_name not in existing_characters:
                # Create character image path
                image_path = f"images/characters/{character_name.lower()}.png"

                # Insert character
                cursor.execute(
                    'INSERT INTO characters (name, image_path) VALUES (?, ?)',
                    (character_name, image_path)
                )

                # Get the new character's ID
                cursor.execute('SELECT id FROM characters WHERE name = ?', (character_name,))
                character_id = cursor.fetchone()['id']

                # Initialize related records
                cursor.execute(
                    'INSERT OR IGNORE INTO shard_inventory (character_id, void1_shards, void2_shards, void3_shards) VALUES (?, 0, 0, 0)',
                    (character_id,)
                )
                cursor.execute(
                    'INSERT OR IGNORE INTO ta_scores (character_id, ta_score) VALUES (?, 0)',
                    (character_id,)
                )
                cursor.execute(
                    'INSERT OR IGNORE INTO gear (character_id) VALUES (?)',
                    (character_id,)
                )
                cursor.execute(
                    'INSERT OR IGNORE INTO abyss_runs (character_id, completed) VALUES (?, 0)',
                    (character_id,)
                )
                cursor.execute(
                    'INSERT OR IGNORE INTO void_raids (character_id, void1, void2, void3, date) VALUES (?, 0, 0, 0, ?)',
                    (character_id, today)
                )
                cursor.execute(
                    'INSERT OR IGNORE INTO accessories (character_id) VALUES (?)',
                    (character_id,)
                )

        conn.commit()
