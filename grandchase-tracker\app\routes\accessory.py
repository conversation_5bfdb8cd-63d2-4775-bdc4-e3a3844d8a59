"""
GrandChase Tracker - Accessory Routes
"""
from flask import Blueprint, request, jsonify, current_app
from app.models.database import get_db_connection

accessory_bp = Blueprint('accessory', __name__)

@accessory_bp.route('/update_accessory', methods=['POST'])
def update_accessory():
    """Update accessory for a character"""
    data = request.get_json()
    character_id = data.get('character_id')
    accessory_type = data.get('accessory_type')  # 'ring', 'earring', or 'piercing'
    accessory_value = data.get('accessory_value')
    
    # Validate accessory type
    if accessory_type not in ['ring', 'earring', 'piercing']:
        return jsonify(success=False, error=f"Invalid accessory type: {accessory_type}")
    
    try:
        db_path = current_app.config['DATABASE_PATH']
        with get_db_connection(db_path) as conn:
            cursor = conn.cursor()
            
            # Update accessory
            cursor.execute(f'''
                UPDATE accessories
                SET {accessory_type} = ?
                WHERE character_id = ?
            ''', (accessory_value, character_id))
            
            # If no rows were updated, insert a new record
            if cursor.rowcount == 0:
                # Get default values from config
                config = current_app.config
                defaults = {
                    'ring': config.get('RING_OPTIONS', ['Harkyon'])[0],
                    'earring': config.get('EARRING_OPTIONS', ['Order'])[0],
                    'piercing': config.get('PIERCING_OPTIONS', ['Order'])[0]
                }
                
                # Override the specified accessory type
                defaults[accessory_type] = accessory_value
                
                cursor.execute('''
                    INSERT INTO accessories (character_id, ring, earring, piercing)
                    VALUES (?, ?, ?, ?)
                ''', (
                    character_id,
                    defaults['ring'],
                    defaults['earring'],
                    defaults['piercing']
                ))
            
            conn.commit()
            
            return jsonify(success=True)
    except Exception as e:
        return jsonify(success=False, error=str(e))
