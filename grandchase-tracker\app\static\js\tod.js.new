/**
 * GrandChase Tracker - Tower of Disappearance (ToD) JavaScript
 * Contains functionality for the ToD Tracker tab
 */

// Update ToD status for a character
function updateToD(characterId, cleared) {
    // Get the selected floor
    const floorSelect = document.getElementById(`floor-select-${characterId}`);
    const floor = parseInt(floorSelect.value);

    // Get the drop data
    const run1_5f = document.getElementById(`run1-5f-${characterId}`).checked;
    const run1_10f = document.getElementById(`run1-10f-${characterId}`).checked;
    const run2_5f = document.getElementById(`run2-5f-${characterId}`).checked;
    const run2_10f = document.getElementById(`run2-10f-${characterId}`).checked;
    const run3_5f = document.getElementById(`run3-5f-${characterId}`).checked;
    const run3_10f = document.getElementById(`run3-10f-${characterId}`).checked;

    // Calculate total SPR dropped (1 per checked box)
    const sprDropped = (run1_5f ? 1 : 0) + (run1_10f ? 1 : 0) +
                      (run2_5f ? 1 : 0) + (run2_10f ? 1 : 0) +
                      (run3_5f ? 1 : 0) + (run3_10f ? 1 : 0);

    // Create detailed data object
    const detailedData = {
        run1_5f: run1_5f,
        run1_10f: run1_10f,
        run2_5f: run2_5f,
        run2_10f: run2_10f,
        run3_5f: run3_5f,
        run3_10f: run3_10f
    };

    axios.post('/update_tod', {
        character_id: characterId,
        cleared: cleared,
        spr_dropped: sprDropped,
        floor: floor,
        detailed_data: detailedData
    })
    .then(function (response) {
        if (response.data.success) {
            console.log('ToD status updated successfully');
            updateToDCount();
            showToast(`ToD ${cleared ? 'completed' : 'reset'} for character #${characterId}`);

            // If we're clearing (un-checking) a character, reset to default floor
            if (!cleared) {
                setTimeout(() => resetCharacterToDefault(characterId), 500);
            }
        } else {
            console.error('Failed to update ToD status:', response.data.error);
            showToast('Error updating ToD status', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error updating ToD status:', error);
        showToast('Error updating ToD status', 'error');
    });
}

// Update ToD completion count
function updateToDCount() {
    axios.get('/get_tod_count')
    .then(function (response) {
        if (response.data.success) {
            const completedCount = response.data.completed_count;
            const totalCharacters = response.data.total_characters;

            // Update counter with progress bar
            updateCounterWithProgressBar('tod-completion', completedCount, totalCharacters);
        } else {
            console.error('Failed to get ToD count:', response.data.error);
            showToast('Error getting ToD count', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error getting ToD count:', error);
        showToast('Error getting ToD count', 'error');
    });
}

// Update ToD options based on selected floor
function updateToDFloorSelection(characterId) {
    const floorSelect = document.getElementById(`floor-select-${characterId}`);
    const selectedFloor = parseInt(floorSelect.value);

    // Show/hide drop toggles based on selected floor
    updateDropTogglesVisibility(characterId, selectedFloor);

    // Update any data saved in the DOM
    saveDropData(characterId);
}

// Show/hide drop toggles based on selected floor
function updateDropTogglesVisibility(characterId, selectedFloor) {
    // Get all drop toggles for this character
    const run1_5f_toggle = document.getElementById(`run1-5f-${characterId}`).parentElement;
    const run1_10f_toggle = document.getElementById(`run1-10f-${characterId}`).parentElement;
    const run2_5f_toggle = document.getElementById(`run2-5f-${characterId}`).parentElement;
    const run2_10f_toggle = document.getElementById(`run2-10f-${characterId}`).parentElement;
    const run3_5f_toggle = document.getElementById(`run3-5f-${characterId}`).parentElement;
    const run3_10f_toggle = document.getElementById(`run3-10f-${characterId}`).parentElement;

    // Show/hide toggles based on selected floor
    if (selectedFloor === 5) {
        // 5F floor - show only 5F toggles
        run1_5f_toggle.style.display = 'flex';
        run1_10f_toggle.style.display = 'none';
        run2_5f_toggle.style.display = 'flex';
        run2_10f_toggle.style.display = 'none';
        run3_5f_toggle.style.display = 'flex';
        run3_10f_toggle.style.display = 'none';

        // Reset 10F toggles
        document.getElementById(`run1-10f-${characterId}`).checked = false;
        document.getElementById(`run2-10f-${characterId}`).checked = false;
        document.getElementById(`run3-10f-${characterId}`).checked = false;
    } else {
        // 10F floor - show both 5F and 10F toggles
        run1_5f_toggle.style.display = 'flex';
        run1_10f_toggle.style.display = 'flex';
        run2_5f_toggle.style.display = 'flex';
        run2_10f_toggle.style.display = 'flex';
        run3_5f_toggle.style.display = 'flex';
        run3_10f_toggle.style.display = 'flex';
    }
}

// Save drop data for a character
function saveDropData(characterId) {
    // Get the checkbox
    const clearedCheckbox = document.querySelector(`tr[data-character-id="${characterId}"] input[type="checkbox"][onchange*="updateToD"]`);
    const isCleared = clearedCheckbox && clearedCheckbox.checked;
    
    // Always save the drop data, regardless of cleared status
    // This ensures drops are saved even if the character isn't marked as cleared yet
    
    // Get the selected floor
    const floorSelect = document.getElementById(`floor-select-${characterId}`);
    const floor = parseInt(floorSelect.value);

    // Get the drop data
    const run1_5f = document.getElementById(`run1-5f-${characterId}`).checked;
    const run1_10f = document.getElementById(`run1-10f-${characterId}`).checked;
    const run2_5f = document.getElementById(`run2-5f-${characterId}`).checked;
    const run2_10f = document.getElementById(`run2-10f-${characterId}`).checked;
    const run3_5f = document.getElementById(`run3-5f-${characterId}`).checked;
    const run3_10f = document.getElementById(`run3-10f-${characterId}`).checked;

    // Calculate total SPR dropped (1 per checked box)
    const sprDropped = (run1_5f ? 1 : 0) + (run1_10f ? 1 : 0) +
                      (run2_5f ? 1 : 0) + (run2_10f ? 1 : 0) +
                      (run3_5f ? 1 : 0) + (run3_10f ? 1 : 0);

    // Create detailed data object
    const detailedData = {
        run1_5f: run1_5f,
        run1_10f: run1_10f,
        run2_5f: run2_5f,
        run2_10f: run2_10f,
        run3_5f: run3_5f,
        run3_10f: run3_10f
    };

    // Save to database
    axios.post('/update_tod', {
        character_id: characterId,
        cleared: isCleared,
        spr_dropped: sprDropped,
        floor: floor,
        detailed_data: detailedData
    })
    .then(function (response) {
        if (response.data.success) {
            console.log('ToD drop data saved successfully');
            // Don't show a toast to avoid spamming the user when toggling multiple drops
        } else {
            console.error('Failed to save ToD drop data:', response.data.error);
        }
    })
    .catch(function (error) {
        console.error('Error saving ToD drop data:', error);
    });
}

// Load existing ToD data for a character
function loadExistingToDData(characterId) {
    // Get today's date
    const today = new Date().toISOString().split('T')[0];

    axios.get(`/get_tod_data?character_id=${characterId}&date=${today}`)
    .then(function (response) {
        if (response.data.success) {
            const floorSelect = document.getElementById(`floor-select-${characterId}`);
            const run1Select = document.getElementById(`run1-${characterId}`);
            const run2Select = document.getElementById(`run2-${characterId}`);
            const run3Select = document.getElementById(`run3-${characterId}`);

            // Set the floor
            if (floorSelect) {
                floorSelect.value = response.data.floor;

                // Update drop toggles visibility based on floor
                updateDropTogglesVisibility(characterId, response.data.floor);
            }

            // Set the detailed drop data if available
            if (response.data.detailed_data) {
                const detailedData = response.data.detailed_data;

                // Check if we have the new format (with floor info)
                if ('run1_5f' in detailedData) {
                    // New format with separate 5F and 10F toggles
                    document.getElementById(`run1-5f-${characterId}`).checked = detailedData.run1_5f;
                    document.getElementById(`run1-10f-${characterId}`).checked = detailedData.run1_10f;
                    document.getElementById(`run2-5f-${characterId}`).checked = detailedData.run2_5f;
                    document.getElementById(`run2-10f-${characterId}`).checked = detailedData.run2_10f;
                    document.getElementById(`run3-5f-${characterId}`).checked = detailedData.run3_5f;
                    document.getElementById(`run3-10f-${characterId}`).checked = detailedData.run3_10f;
                } else if ('run1' in detailedData) {
                    // Legacy format - convert to new format
                    // For legacy data, we'll just mark drops in the floor that matches the character's current floor
                    const isFloor5 = response.data.floor === 5;

                    if (detailedData.run1 > 0) {
                        document.getElementById(`run1-${isFloor5 ? '5f' : '10f'}-${characterId}`).checked = true;
                    }

                    if (detailedData.run2 > 0) {
                        document.getElementById(`run2-${isFloor5 ? '5f' : '10f'}-${characterId}`).checked = true;
                    }

                    if (detailedData.run3 > 0) {
                        document.getElementById(`run3-${isFloor5 ? '5f' : '10f'}-${characterId}`).checked = true;
                    }
                }
            } else {
                // If no detailed data, distribute the total SPR dropped evenly
                const totalSpr = response.data.spr_dropped;
                const isFloor5 = response.data.floor === 5;

                if (totalSpr > 0) {
                    // Simple distribution algorithm
                    let remaining = totalSpr;

                    if (remaining > 0) {
                        document.getElementById(`run1-${isFloor5 ? '5f' : '10f'}-${characterId}`).checked = true;
                        remaining--;
                    }

                    if (remaining > 0) {
                        document.getElementById(`run2-${isFloor5 ? '5f' : '10f'}-${characterId}`).checked = true;
                        remaining--;
                    }

                    if (remaining > 0) {
                        document.getElementById(`run3-${isFloor5 ? '5f' : '10f'}-${characterId}`).checked = true;
                    }
                }
            }
        }
    })
    .catch(function (error) {
        console.error('Error loading ToD data:', error);
    });
}

// Save default floor preference
function saveDefaultFloorPreference() {
    const defaultFloorSelect = document.getElementById('default-floor-preference');
    if (defaultFloorSelect) {
        localStorage.setItem('defaultToDFloor', defaultFloorSelect.value);
        showToast(`Default floor set to ${defaultFloorSelect.options[defaultFloorSelect.selectedIndex].text}`);
    }
}

// Apply default floor to all characters
function applyDefaultFloorToAll() {
    const defaultFloorSelect = document.getElementById('default-floor-preference');
    if (!defaultFloorSelect) return;

    const defaultFloor = defaultFloorSelect.value;

    // Get all floor selects
    const floorSelects = document.querySelectorAll('select[id^="floor-select-"]');

    floorSelects.forEach(select => {
        select.value = defaultFloor;

        // Get character ID from select ID
        const characterId = select.id.replace('floor-select-', '');

        // Update options for this character
        updateToDFloorSelection(characterId);
    });

    showToast(`Applied ${defaultFloorSelect.options[defaultFloorSelect.selectedIndex].text} to all characters`);
}

// Reset character to default floor
function resetCharacterToDefault(characterId) {
    const defaultFloor = localStorage.getItem('defaultToDFloor') || '10';
    const floorSelect = document.getElementById(`floor-select-${characterId}`);

    if (floorSelect && floorSelect.value !== defaultFloor) {
        floorSelect.value = defaultFloor;
        updateToDFloorSelection(characterId);
    }

    // Reset run toggles
    document.getElementById(`run1-5f-${characterId}`).checked = false;
    document.getElementById(`run1-10f-${characterId}`).checked = false;
    document.getElementById(`run2-5f-${characterId}`).checked = false;
    document.getElementById(`run2-10f-${characterId}`).checked = false;
    document.getElementById(`run3-5f-${characterId}`).checked = false;
    document.getElementById(`run3-10f-${characterId}`).checked = false;
}

// Load ToD history data
function loadToDHistory() {
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;

    if (!startDate || !endDate) {
        showToast('Please select start and end dates', 'error');
        return;
    }

    axios.get(`/get_tod_history?start_date=${startDate}&end_date=${endDate}`)
    .then(function (response) {
        if (response.data.success) {
            // Update history table
            updateToDHistoryTable(response.data.totals);
        } else {
            console.error('Failed to get ToD history:', response.data.error);
            showToast('Error loading ToD history', 'error');
        }
    })
    .catch(function (error) {
        console.error('Error getting ToD history:', error);
        showToast('Error loading ToD history', 'error');
    });
}

// Update ToD history table
function updateToDHistoryTable(totals) {
    // Get the character and run counts
    const floor5_chars = totals.floor5_chars || 0;
    const floor10_chars = totals.floor10_chars || 0;
    const floor5_runs = totals.floor5_runs || 0;
    const floor10_runs = totals.floor10_runs || 0;

    // Calculate additional metrics
    const totalRuns = floor5_runs + floor10_runs;
    const totalDrops = totals.total_spr;
    const avgDropRate = totalRuns > 0 ? ((totalDrops / totalRuns) * 100).toFixed(2) : '0.00';

    // Get the drop data from the new format
    const floor5_drops_from_5f = totals.floor5_spr_from_5f || 0;
    const floor5_drops_from_10f = totals.floor5_spr_from_10f || 0;
    const floor10_drops = totals.floor10_spr || 0;

    // Get the drop rates directly from the backend
    const floor5_rate_5f = totals.floor5_rate_5f || 0;
    const floor5_rate_10f = totals.floor5_rate_10f || 0;
    const floor10_rate = totals.floor10_rate || 0;

    // Determine best drop floor
    let bestFloor = '-';
    if (floor5_rate_5f > 0 || floor5_rate_10f > 0 || floor10_rate > 0) {
        const rates = [
            { floor: '5F', rate: floor5_rate_5f },
            { floor: '5F (10F runs)', rate: floor5_rate_10f },
            { floor: '10F', rate: floor10_rate }
        ];
        rates.sort((a, b) => b.rate - a.rate);
        bestFloor = rates[0].rate > 0 ? rates[0].floor : '-';

        // Simplify to just 10F if that's the best
        if (bestFloor === '5F (10F runs)' && rates[0].rate === floor10_rate) {
            bestFloor = '10F';
        }
    }

    // Update summary cards
    document.getElementById('total-runs').textContent = totalRuns;
    document.getElementById('total-drops').textContent = totalDrops;
    document.getElementById('avg-drop-rate').textContent = `${avgDropRate}%`;
    document.getElementById('best-drop-floor').textContent = bestFloor;

    // Update run stats
    document.getElementById('5f-clears').textContent = floor5_chars;
    document.getElementById('10f-clears').textContent = floor10_chars;

    // Update drop analysis
    document.getElementById('5f-drops-from-5f').textContent = floor5_drops_from_5f;
    document.getElementById('5f-drops-from-10f').textContent = floor5_drops_from_10f;
    document.getElementById('10f-drops').textContent = floor10_drops;

    // Update drop rates
    document.getElementById('5f-drop-rate-5f').textContent = `${floor5_rate_5f}%`;
    document.getElementById('5f-drop-rate-10f').textContent = `${floor5_rate_10f}%`;
    document.getElementById('10f-drop-rate').textContent = `${floor10_rate}%`;

    // Update comparison bars
    updateComparisonBar('5f-5f-bar', floor5_rate_5f);
    updateComparisonBar('5f-10f-bar', floor5_rate_10f);
    updateComparisonBar('10f-bar', floor10_rate);
}

// Update a comparison bar
function updateComparisonBar(id, rate) {
    const bar = document.getElementById(id);
    if (bar) {
        // Animate the width change
        setTimeout(() => {
            bar.style.width = `${Math.min(rate, 100)}%`;
            bar.textContent = `${rate}%`;
        }, 100);
    }
}

// Display the current date being used for ToD runs
function displayCurrentToDDate() {
    // Get the current date in UTC
    const now = new Date();
    
    // Get the reset hour from the server config (default to 6 UTC)
    const resetHourUTC = 6; // This should match the RESET_HOUR_UTC in the server config
    
    // Create a date object for the reset time today
    const resetTime = new Date(now);
    resetTime.setUTCHours(resetHourUTC, 0, 0, 0);
    
    // Determine if we're before or after the reset time
    let todDate;
    if (now < resetTime) {
        // Before reset, use yesterday's date
        todDate = new Date(now);
        todDate.setDate(todDate.getDate() - 1);
    } else {
        // After reset, use today's date
        todDate = now;
    }
    
    // Format the date as DD/MM/YYYY
    const day = todDate.getDate().toString().padStart(2, '0');
    const month = (todDate.getMonth() + 1).toString().padStart(2, '0');
    const year = todDate.getFullYear();
    const formattedDate = `${day}/${month}/${year}`;
    
    // Update the date display
    const dateDisplay = document.getElementById('tod-current-date');
    if (dateDisplay) {
        dateDisplay.textContent = formattedDate;
    }
}

// Initialize ToD tracker when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Display the current date being used for ToD runs
    displayCurrentToDDate();
    
    // Initialize ToD drop options for all characters
    document.querySelectorAll('tr[data-character-id]').forEach(row => {
        const characterId = row.dataset.characterId;
        const floorSelect = document.getElementById(`floor-select-${characterId}`);

        if (floorSelect) {
            // Initialize ToD options based on selected floor
            updateToDFloorSelection(characterId);

            // Load existing drop data if available
            loadExistingToDData(characterId);
        }
    });

    // Load the saved preference from localStorage
    const savedFloor = localStorage.getItem('defaultToDFloor');
    if (savedFloor) {
        const defaultFloorSelect = document.getElementById('default-floor-preference');
        if(defaultFloorSelect) {
            defaultFloorSelect.value = savedFloor;
        }
    }
});
