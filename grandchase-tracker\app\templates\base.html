<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}GrandChase Tracker{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    {% block head %}{% endblock %}
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="{{ url_for('static', filename='images/logo/logo.jpeg') }}" alt="GrandChase Tracker" class="app-logo">
            </div>
            <button id="dark-mode-toggle" class="dark-mode-toggle">
                <i class="fas fa-moon"></i>
                <span id="theme-text">Toggle Dark Mode</span>
            </button>
        </div>

        <div class="controls">
            <button onclick="resetVoid1()">Reset Void 1</button>
            <button onclick="resetVoid2()">Reset Void 2</button>
            <button onclick="resetVoid3()">Reset Void 3</button>
            <button onclick="resetAbyss()">Reset Abyss</button>
            <button onclick="toggleTATierList()" class="ta-tier-list-toggle"><i class="fas fa-trophy"></i> Show/Hide TA Tier List</button>
        </div>

        <div class="tabs">
            <button class="tab-button active" onclick="showTab('overview')">Overview</button>
            <button class="tab-button" onclick="showTab('void-tracker')">Void Tracker</button>
            <button class="tab-button" onclick="showTab('ta-tracker')">TA Tracker</button>
            <button class="tab-button" onclick="showTab('gear-tracker')">Gear Tracker</button>
            <button class="tab-button" onclick="showTab('abyss-tracker')">Abyss Tracker</button>
            <button class="tab-button" onclick="showTab('accessory-tracker')">Accessory Tracker</button>
            <button class="tab-button" onclick="showTab('tod-tracker')">ToD Tracker</button>
            <button class="tab-button" onclick="showTab('tod-history')">ToD History</button>
        </div>

        <div id="toast-container"></div>

        {% block content %}{% endblock %}
    </div>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/overview.js') }}"></script>
    <script src="{{ url_for('static', filename='js/void.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ta.js') }}"></script>
    <script src="{{ url_for('static', filename='js/gear.js') }}"></script>
    <script src="{{ url_for('static', filename='js/abyss.js') }}"></script>
    <script src="{{ url_for('static', filename='js/tod.js') }}"></script>
    <script src="{{ url_for('static', filename='js/accessory.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
